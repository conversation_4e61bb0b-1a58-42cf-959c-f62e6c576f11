import {Lab} from '@regrow-internal/design-system';

import type {FieldEvent, FieldEventValues} from '__generated__/gql/graphql';

import {croppingEvents} from 'views/mrv/project/phases/stages/__tests__/mock-data/mocks';
import type {FieldEventRowModel} from 'views/mrv/project/phases/stages/field-events/field-event-table/table-components/types';
import {
  getDefaultRowEditState,
  getFieldPracticeRowEditState,
  getReferenceDate,
  getRows,
  isPrefilledRow,
  shouldRowBeInEditMode,
} from 'views/mrv/project/phases/stages/field-events/field-event-table/table-components/utils';
import type {FieldCultivationCycle} from 'views/mrv/project/phases/stages/field-events/FieldEventContext';
import {FieldEventType} from 'views/mrv/project/phases/stages/field-events/types';
import {convertStringToDate} from 'views/mrv/project/phases/stages/field-events/utils/dateUtils';

const cultivationCycle = {
  id: 'cult-1',
  start_date: '2023-01-12',
  end_date: '2023-12-12',
  name: 'Cultivation cycle name',
  events: [],
};
const events = croppingEvents() as Array<FieldEvent>;
const cultivationCycles: Array<FieldCultivationCycle> = [
  cultivationCycle,
  {...cultivationCycle, events, id: 'cult-2'},
];

const meta = {
  type: FieldEventType.TillageEvent,
  eventCount: 0,
  showCultivationCycle: true,
  isLocked: false,
  isReadOnly: false,
  cultivationCycle: {
    no_practice_observations: {
      tillage_event: true,
      irrigation_event: true,
      application_event: true,
    },
    end_date: '2023-12-31',
    id: '123',
    start_date: '2023-01-01',
    crop_type: 'corn',
    name: 'corn 2023',
  },
};

describe('TableComponents/utils', () => {
  describe('getRows', () => {
    it('should return an empty array if no cultivation cycles', () => {
      const rows = getRows(null);
      expect(rows).toEqual([]);
    });

    it('should return an array of field event row models', () => {
      const rows = getRows(cultivationCycles);
      expect(rows).toEqual([
        {
          id: cultivationCycles[0]?.id,
          meta: {
            cultivationCycle: {...cultivationCycles[0], events: undefined},
            showCultivationCycle: true,
            type: 'CultivationCycle',
            eventCount: 0,
            isPrefill: false,
          },
        },
        {
          id: cultivationCycles[1]?.events?.[0]?.id,
          ...cultivationCycles[1]?.events?.[0]?.event_values,
          meta: {
            cultivationCycle: {...cultivationCycles[1], events: undefined},
            showCultivationCycle: true,
            type: FieldEventType.CroppingEvent,
            eventCount: 2,
            isPrefill: false,
            isUnsavedEvent: false,
          },
        },
        {
          id: cultivationCycles[1]?.events?.[1]?.id,
          ...cultivationCycles[1]?.events?.[1]?.event_values,
          meta: {
            cultivationCycle: {...cultivationCycles[1], events: undefined},
            showCultivationCycle: false,
            type: FieldEventType.CroppingEvent,
            eventCount: 2,
            isPrefill: false,
            isUnsavedEvent: false,
          },
        },
      ]);
    });
  });

  describe('shouldRowBeInEditMode', () => {
    it('should return false for unknown event type', () => {
      const result = shouldRowBeInEditMode(meta, 'unknown' as FieldEventType, false);
      expect(result).toBe(false);
    });

    describe('Cropping events', () => {
      it('should return true for cultivation cycles with no crop_type', () => {
        const result = shouldRowBeInEditMode(
          {...meta, cultivationCycle: {...meta.cultivationCycle, crop_type: null}},
          FieldEventType.CroppingEvent,
          false
        );
        expect(result).toBe(true);
      });

      it('should return false for cultivation cycles with crop_type', () => {
        const result = shouldRowBeInEditMode(meta, FieldEventType.CroppingEvent, false);
        expect(result).toBe(false);
      });
    });

    describe('Field practice events', () => {
      it('should return true for unsaved events', () => {
        const result = shouldRowBeInEditMode(
          {...meta, isUnsavedEvent: true},
          FieldEventType.TillageEvent,
          false
        );
        expect(result).toBe(true);
      });

      it('should return false for saved events', () => {
        const result = shouldRowBeInEditMode(
          {...meta, isUnsavedEvent: false},
          FieldEventType.TillageEvent,
          false
        );
        expect(result).toBe(false);
      });
    });

    describe('canBypassEventLock permission', () => {
      it('should return true for read-only events when user has bypass permission', () => {
        const result = shouldRowBeInEditMode(
          {...meta, isUnsavedEvent: true, isReadOnly: true},
          FieldEventType.TillageEvent,
          true
        );
        expect(result).toBe(true);
      });
    });
  });

  describe('getFieldPracticeRowEditState', () => {
    const trueResult = {
      ...meta,
      cultivationCycle: {
        ...meta.cultivationCycle,
        no_practice_observations: {application_event: false},
      },
      isUnsavedEvent: false,
      eventCount: 0,
    };

    it('should return null edit state for non-field practice stages', () => {
      const result = getFieldPracticeRowEditState(
        {...trueResult, cultivationCycle: {...trueResult.cultivationCycle, crop_type: null}},
        FieldEventType.CroppingEvent
      );
      expect(result).toEqual({
        isFieldPracticeCultivationCycle: false,
        editState: null,
      });
    });

    it('should return null edit state for cultivation cycles with no crop_type', () => {
      const result = getFieldPracticeRowEditState(
        {...trueResult, cultivationCycle: {...trueResult.cultivationCycle, crop_type: null}},
        FieldEventType.TillageEvent
      );
      expect(result).toEqual({
        isFieldPracticeCultivationCycle: true,
        editState: null,
      });
    });

    it('should return null edit state for hidden cultivation cycles', () => {
      const result = getFieldPracticeRowEditState(
        {
          ...trueResult,
          cultivationCycle: {...trueResult.cultivationCycle, crop_type: 'corn'},
          showCultivationCycle: false,
        },
        FieldEventType.TillageEvent
      );
      expect(result).toEqual({
        isFieldPracticeCultivationCycle: true,
        editState: null,
      });
    });

    it('should return view mode if no practice observation is true', () => {
      const result = getFieldPracticeRowEditState(meta, FieldEventType.TillageEvent);
      expect(result).toEqual({
        isFieldPracticeCultivationCycle: true,
        editState: Lab.GridRowModes.View,
      });
    });

    it('should return null if practice observation is false and events exist for the cycle', () => {
      const result = getFieldPracticeRowEditState(
        {
          ...trueResult,
          cultivationCycle: {
            ...trueResult.cultivationCycle,
            no_practice_observations: {irrigation_event: false},
          },
          eventCount: 1,
        },
        FieldEventType.IrrigationEvent
      );
      expect(result).toEqual({
        isFieldPracticeCultivationCycle: true,
        editState: null,
      });
    });

    it('should return edit state if practice observation is false and no events exist for the cycle', () => {
      const result = getFieldPracticeRowEditState(
        {
          ...trueResult,
          cultivationCycle: {
            ...trueResult.cultivationCycle,
            no_practice_observations: {application_event: false},
          },
          eventCount: 0,
        },
        FieldEventType.ApplicationEvent
      );
      expect(result).toEqual({
        isFieldPracticeCultivationCycle: true,
        editState: Lab.GridRowModes.Edit,
      });
    });

    it('should return view mode if is unsaved event when no practice oversation is true', () => {
      const result = getFieldPracticeRowEditState(
        {
          ...meta,
          cultivationCycle: {
            ...meta.cultivationCycle,
            no_practice_observations: {application_event: true},
          },
          isUnsavedEvent: true,
          eventCount: 1,
        },
        FieldEventType.ApplicationEvent
      );
      expect(result).toEqual({
        isFieldPracticeCultivationCycle: true,
        editState: Lab.GridRowModes.View,
      });
    });

    it('should return edit mode if is unsaved event', () => {
      const result = getFieldPracticeRowEditState(
        {
          ...meta,
          cultivationCycle: {
            ...meta.cultivationCycle,
            no_practice_observations: {application_event: false},
          },
          isUnsavedEvent: true,
          eventCount: 1,
        },
        FieldEventType.ApplicationEvent
      );
      expect(result).toEqual({
        isFieldPracticeCultivationCycle: true,
        editState: Lab.GridRowModes.Edit,
      });
    });
  });

  describe('getDefaultRowEditState', () => {
    describe('Cropping event row', () => {
      it('should return null if row is not in edit state', () => {
        const result = getDefaultRowEditState(
          {
            meta: {
              ...meta,
              isUnsavedEvent: false,
            },
            id: '1',
          },
          FieldEventType.CroppingEvent
        );
        expect(result).toEqual(null);
      });

      it('should return edit mode if row is in edit state', () => {
        const result = getDefaultRowEditState(
          {
            meta: {
              ...meta,
              isUnsavedEvent: true,
            },
            id: '1',
          },
          FieldEventType.CroppingEvent
        );
        expect(result).toEqual({
          mode: Lab.GridRowModes.Edit,
          ignoreModifications: true,
        });
      });
    });

    describe('Field practices row', () => {
      it('should return edit state for no crop cultivation cycle rows', () => {
        const result = getDefaultRowEditState(
          {
            meta: {
              ...meta,
              cultivationCycle: {
                ...meta.cultivationCycle,
                crop_type: null,
              },
              showCultivationCycle: true,
              isUnsavedEvent: false,
              eventCount: 0,
            },
            id: '1',
          },
          FieldEventType.TillageEvent
        );
        expect(result).toEqual({ignoreModifications: true, mode: 'edit'});
      });

      it('should return null if cultivation cycle row is hidden', () => {
        const result = getDefaultRowEditState(
          {
            meta: {
              ...meta,
              cultivationCycle: {
                ...meta.cultivationCycle,
                crop_type: 'corn',
              },
              showCultivationCycle: false,
              isUnsavedEvent: false,
              eventCount: 0,
            },
            id: '1',
          },
          FieldEventType.TillageEvent
        );
        expect(result).toEqual(null);
      });

      it('should set edit state if in edit mode', () => {
        const result = getDefaultRowEditState(
          {
            meta: {
              ...meta,
              cultivationCycle: {
                ...meta.cultivationCycle,
                no_practice_observations: {tillage_event: false},
              },
              isUnsavedEvent: false,
              eventCount: 0,
            },
            id: '1',
          },
          FieldEventType.TillageEvent
        );
        expect(result).toEqual({
          mode: Lab.GridRowModes.Edit,
          ignoreModifications: true,
        });
      });

      it('should set view state if in edit mode', () => {
        const result = getDefaultRowEditState(
          {
            meta: {
              ...meta,
              cultivationCycle: {
                ...meta.cultivationCycle,
                no_practice_observations: {tillage_event: true},
              },
              isUnsavedEvent: false,
              eventCount: 0,
            },
            id: '1',
          },
          FieldEventType.TillageEvent
        );
        expect(result).toEqual({
          mode: Lab.GridRowModes.View,
          ignoreModifications: true,
        });
      });
    });
  });

  describe('getReferenceDate', () => {
    it('should return the start dates first', () => {
      const row: FieldEventRowModel = {
        id: '1',
        start_date: '2023-01-01',
        end_date: '2023-05-01',
        planting_date: '2023-12-31',
        harvest_date: '2023-12-31',
        meta: {
          isLocked: false,
          cultivationCycle,
          type: 'CroppingEvent',
          eventCount: 0,
          isReadOnly: false,
        },
      };
      const start_date_first = getReferenceDate(row);
      expect(start_date_first).toEqual(convertStringToDate(String(row.start_date)));

      const harvest_date_first = getReferenceDate({...row, start_date: null});
      expect(harvest_date_first).toEqual(convertStringToDate(String(row.harvest_date)));
    });

    it('should return the end date if no start date', () => {
      const row: FieldEventRowModel = {
        id: '1',
        start_date: null,
        end_date: '2023-01-01',
        planting_date: '2023-12-31',
        harvest_date: null,
        meta: {
          isLocked: false,
          cultivationCycle,
          type: 'CroppingEvent',
          eventCount: 0,
          isReadOnly: false,
        },
      };
      const end_date_first = getReferenceDate(row);
      expect(end_date_first).toEqual(convertStringToDate(String(row.end_date)));

      const planting_date_first = getReferenceDate({...row, end_date: null});
      expect(planting_date_first).toEqual(convertStringToDate(String(row.planting_date)));
    });

    it('should return cultivation cycle start dates if no other dates', () => {
      const row: FieldEventRowModel = {
        id: '1',
        start_date: null,
        end_date: null,
        planting_date: null,
        harvest_date: null,
        meta: {
          isLocked: false,
          cultivationCycle,
          type: 'CroppingEvent',
          eventCount: 0,
          isReadOnly: false,
        },
      };
      const cultivation_cycle_start_date = getReferenceDate(row);
      expect(cultivation_cycle_start_date).toEqual(
        convertStringToDate(cultivationCycle.start_date)
      );
    });

    it('should return null if no valid date', () => {
      const row: FieldEventRowModel = {
        id: '1',
        start_date: null,
        end_date: null,
        planting_date: null,
        harvest_date: null,
        meta: {
          isLocked: false,
          cultivationCycle: undefined,
          type: 'CroppingEvent',
          eventCount: 0,
          isReadOnly: false,
        },
      };
      const null_date = getReferenceDate(row);
      expect(null_date).toBeNull();
    });
  });

  describe('isPrefilledRow', () => {
    describe('CroppingEvent', () => {
      it('should return true when both planting_date and harvest_date are empty', () => {
        const eventValues = {
          __typename: FieldEventType.CroppingEvent,
          planting_date: null,
          harvest_date: null,
        } as FieldEventValues;

        const result = isPrefilledRow(eventValues);

        expect(result).toBe(true);
      });

      it('should return false when planting_date has a value', () => {
        const eventValues = {
          __typename: FieldEventType.CroppingEvent,
          planting_date: '2023-01-01',
          harvest_date: null,
        } as FieldEventValues;

        const result = isPrefilledRow(eventValues);

        expect(result).toBe(false);
      });

      it('should return false when harvest_date has a value', () => {
        const eventValues = {
          __typename: FieldEventType.CroppingEvent,
          planting_date: null,
          harvest_date: '2023-12-01',
        } as FieldEventValues;

        const result = isPrefilledRow(eventValues);

        expect(result).toBe(false);
      });

      it('should return false when both dates have values', () => {
        const eventValues = {
          __typename: FieldEventType.CroppingEvent,
          planting_date: '2023-01-01',
          harvest_date: '2023-12-01',
        } as FieldEventValues;

        const result = isPrefilledRow(eventValues);

        expect(result).toBe(false);
      });
    });

    describe('FallowPeriod', () => {
      it('should return true when both start_date and end_date are empty', () => {
        const eventValues = {
          __typename: FieldEventType.FallowPeriod,
          start_date: null,
          end_date: null,
        } as FieldEventValues;

        const result = isPrefilledRow(eventValues);

        expect(result).toBe(true);
      });

      it('should return false when start_date has a value', () => {
        const eventValues = {
          __typename: FieldEventType.FallowPeriod,
          start_date: '2023-01-01',
          end_date: null,
        } as FieldEventValues;

        const result = isPrefilledRow(eventValues);

        expect(result).toBe(false);
      });
    });

    describe('ApplicationEvent', () => {
      it('should return true when application_date is empty', () => {
        const eventValues = {
          __typename: FieldEventType.ApplicationEvent,
          application_date: null,
        } as FieldEventValues;

        const result = isPrefilledRow(eventValues);

        expect(result).toBe(true);
      });

      it('should return false when application_date has a value', () => {
        const eventValues = {
          __typename: FieldEventType.ApplicationEvent,
          application_date: '2023-06-01',
        } as FieldEventValues;

        const result = isPrefilledRow(eventValues);

        expect(result).toBe(false);
      });
    });

    describe('TillageEvent', () => {
      it('should return true when tillage_date is empty', () => {
        const eventValues = {
          __typename: FieldEventType.TillageEvent,
          tillage_date: null,
        } as FieldEventValues;

        const result = isPrefilledRow(eventValues);

        expect(result).toBe(true);
      });

      it('should return false when tillage_date has a value', () => {
        const eventValues = {
          __typename: FieldEventType.TillageEvent,
          tillage_date: '2023-03-15',
        } as FieldEventValues;

        const result = isPrefilledRow(eventValues);

        expect(result).toBe(false);
      });
    });

    describe('IrrigationEvent', () => {
      it('should return true when both start_date and end_date are empty', () => {
        const eventValues = {
          __typename: FieldEventType.IrrigationEvent,
          start_date: null,
          end_date: null,
        } as FieldEventValues;

        const result = isPrefilledRow(eventValues);

        expect(result).toBe(true);
      });

      it('should return false when start_date has a value', () => {
        const eventValues = {
          __typename: FieldEventType.IrrigationEvent,
          start_date: '2023-07-01',
          end_date: null,
        } as FieldEventValues;

        const result = isPrefilledRow(eventValues);

        expect(result).toBe(false);
      });
    });

    describe('Edge cases', () => {
      it('should return false for unknown event types', () => {
        const eventValues = {
          __typename: 'UnknownEventType' as any,
        } as FieldEventValues;

        const result = isPrefilledRow(eventValues);

        expect(result).toBe(false);
      });

      it('should handle empty string values', () => {
        const eventValues = {
          __typename: FieldEventType.CroppingEvent,
          planting_date: '',
          harvest_date: '',
        } as FieldEventValues;

        const result = isPrefilledRow(eventValues);

        expect(result).toBe(true);
      });
    });
  });
});
