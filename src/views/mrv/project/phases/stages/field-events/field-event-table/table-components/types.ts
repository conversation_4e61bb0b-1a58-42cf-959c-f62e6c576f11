import type {Lab, TextFieldProps} from '@regrow-internal/design-system';

import type {DateRange, FieldEventValues} from '__generated__/gql/graphql';
import type {StageTypes} from '__generated__/mrv/mrvApi.types';
import type {UnionToIntersection} from '_common/utils/type-utils';
import type {FormatMessage} from '_translations/types';

import type {FieldCultivationCycle} from 'views/mrv/project/phases/stages/field-events/FieldEventContext';
import {FieldEventType} from 'views/mrv/project/phases/stages/field-events/types';
import type {
  AttributeOption,
  FormattedAttributeOptions,
} from 'views/mrv/project/phases/stages/field-events/utils/formatAttributeOptions';

export type PreProcessEditCellPropsProps = Lab.GridPreProcessEditCellProps<
  unknown,
  FieldEventRowModel
>;

export type BaseColumnProps = {
  attributeType: string;
  stageType: StageTypes;
  formatMessage: FormatMessage;
  validator?: (
    eventValues: FieldEventRowModel<FieldEventValues>,
    formatMessage: FormatMessage
  ) => string | null;
};

export type TableInputProps = Pick<
  TextFieldProps,
  | 'aria-label'
  | 'placeholder'
  | 'required'
  | 'disabled'
  | 'value'
  | 'name'
  | 'color'
  | 'inputRef'
  | 'title'
> & {
  formatMessage: FormatMessage;
  error?: string | null;
  options?: Array<AttributeOption>;
  min?: string | null;
  max?: string | null;
  hasFocus?: boolean;
  readOnly?: boolean;
};

export type TableDateInputProps = {
  onChange: (newValue: Date | null) => void;
  value: Date | null;
  referenceDate?: Date | null;
};

export type FieldEventRowModel<T = FieldEventValues> = {
  id: string;
  meta: {
    type: string;
    cultivationCycle?: FieldCultivationCycle;
    isUnsavedEvent?: boolean;
    showCultivationCycle?: boolean;
    eventCount: number;
    isLocked: boolean;
    isReadOnly: boolean;
    isPrefill?: boolean;
  };
} & UnionToIntersection<T>;

export type FieldEventGridColDef<
  T = FieldEventValues,
  /** Ideally `Value` should also be generic based on the input so we can correctly use boolean | string dependently*/
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Value = any | undefined,
> = Lab.GridColDef<FieldEventRowModel<T>, Value>;

export type EventRowProps = {
  attributeOptions: FormattedAttributeOptions;
  formatMessage: FormatMessage;
  dateRange?: DateRange | null;
};

export enum NoPracticeObservationType {
  TillageEvent = 'tillage_event',
  IrrigationEvent = 'irrigation_event',
  ApplicationEvent = 'application_event',
}

export const NO_PRACTICE_OBSERVATION_BY_EVENT_TYPE: Record<string, NoPracticeObservationType> = {
  [FieldEventType.TillageEvent]: NoPracticeObservationType.TillageEvent,
  [FieldEventType.IrrigationEvent]: NoPracticeObservationType.IrrigationEvent,
  [FieldEventType.ApplicationEvent]: NoPracticeObservationType.ApplicationEvent,
};

export interface EditableRow {
  id: Lab.GridRowId;
  hasError: boolean;
  canSave: boolean;
}
