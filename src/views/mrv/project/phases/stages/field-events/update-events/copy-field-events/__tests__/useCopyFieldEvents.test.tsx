import type {MockedProviderProps} from '@apollo/client/testing';
import {MockedProvider} from '@apollo/client/testing';
import {act, waitFor} from '@testing-library/react';
import {renderHook} from '@testing-library/react-hooks';
import type {ReactNode} from 'react';
import React from 'react';

import {CopyFieldEventsMode} from '__generated__/gql/graphql';
import {showNotification} from '_common/components/NotificationSnackbar';
import {SimpleProviders} from '_common/test_utils/renderWithProviders';

import {mockSelectedFieldContext} from 'views/mrv/project/phases/stages/__mocks__/SelectedFieldContext';
import {mockFieldId} from 'views/mrv/project/phases/stages/__tests__/mock-data/mocks';
import {useCopyFieldEvents} from 'views/mrv/project/phases/stages/field-events/update-events/copy-field-events/useCopyFieldEvents';
import {refetchFieldEvents} from 'views/mrv/project/phases/stages/field-events/utils/refetchFieldEvents';
import {useSelectedFieldContext} from 'views/mrv/project/phases/stages/SelectedFieldContext';

import {
  BULK_COPY_FIELD_EVENTS_ERROR_MOCK,
  BULK_COPY_FIELD_EVENTS_SUCCESS_MOCK,
  mockSelectedFieldEvents,
} from './mockData';

jest.mock('views/mrv/project/phases/stages/SelectedFieldContext');
jest.mock('views/mrv/project/phases/PhaseContext');
jest.mock('views/mrv/project/phases/stages/StageContext');
jest.mock('store/useRedux');
jest.mock('views/mrv/project/phases/stages/field-events/utils/refetchFieldEvents');
jest.mock('_common/hooks/use-parsed-match-params', () => ({
  useParsedMatchParams: jest.fn().mockReturnValue({
    projectId: '1',
  }),
}));

jest.mock('_common/components/NotificationSnackbar', () => ({
  showNotification: jest.fn(),
}));

const renderTestComponent = (mocks: MockedProviderProps['mocks'], children: ReactNode) => {
  return (
    <SimpleProviders>
      <MockedProvider mocks={mocks}>{children}</MockedProvider>
    </SimpleProviders>
  );
};

const renderHookWithProviders = (mocks: Array<any> = []) => {
  return renderHook(
    () =>
      useCopyFieldEvents({
        selectedFieldIds: [mockFieldId],
        onComplete: jest.fn(),
      }),
    {
      wrapper: ({children}) => renderTestComponent(mocks, children),
    }
  );
};

describe('useCopyFieldEvents', () => {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  const mockUseSelectedFieldContext = useSelectedFieldContext as jest.MockedFunction<
    typeof useSelectedFieldContext
  >;
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  const mockRefetchFieldEvents = refetchFieldEvents as jest.MockedFunction<
    typeof refetchFieldEvents
  >;

  beforeEach(() => {
    mockUseSelectedFieldContext.mockReturnValue({
      ...mockSelectedFieldContext,
      selectedFieldEvents: mockSelectedFieldEvents,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return copyFieldEvents function and loading state', () => {
    const {result} = renderHookWithProviders();

    expect(result.current.copyFieldEvents).toBeInstanceOf(Function);
    expect(result.current.copyFieldEventsLoading).toBe(false);
  });

  it('should successfully copy field events', async () => {
    const onComplete = jest.fn();
    renderHookWithProviders([BULK_COPY_FIELD_EVENTS_SUCCESS_MOCK]);

    const hookResult = renderHook(
      () =>
        useCopyFieldEvents({
          selectedFieldIds: [mockFieldId],
          onComplete,
        }),
      {
        wrapper: ({children}) => (
          <MockedProvider mocks={[BULK_COPY_FIELD_EVENTS_SUCCESS_MOCK]} addTypename={false}>
            {children}
          </MockedProvider>
        ),
      }
    );

    await act(async () => {
      await hookResult.result.current.copyFieldEvents(CopyFieldEventsMode.Copy);
    });
    await waitFor(() => {
      expect(onComplete).toHaveBeenCalledTimes(1);
    });
  });

  it('should handle copy field events error', async () => {
    const onComplete = jest.fn();

    const hookResult = renderHook(
      () =>
        useCopyFieldEvents({
          selectedFieldIds: [mockFieldId],
          onComplete,
        }),
      {
        wrapper: ({children}) => (
          <MockedProvider mocks={[BULK_COPY_FIELD_EVENTS_ERROR_MOCK]} addTypename={false}>
            {children}
          </MockedProvider>
        ),
      }
    );

    await act(async () => {
      await hookResult.result.current.copyFieldEvents(CopyFieldEventsMode.Overwrite);
    });

    await waitFor(() => {
      expect(showNotification).toHaveBeenCalledWith({
        message: 'There was an issue copying your field events',
        type: 'error',
      });
    });

    expect(onComplete).not.toHaveBeenCalled();
  });

  it('should not copy events when selectedFieldEvents.events is null', async () => {
    mockUseSelectedFieldContext.mockReturnValue({
      ...mockSelectedFieldContext,
      selectedFieldEvents: {
        field: {id: '108264', name: 'Test Field'},
        events: null,
      },
    });

    const onComplete = jest.fn();
    const hookResult = renderHook(
      () =>
        useCopyFieldEvents({
          selectedFieldIds: [mockFieldId],
          onComplete,
        }),
      {
        wrapper: ({children}) => (
          <MockedProvider mocks={[]} addTypename={false}>
            {children}
          </MockedProvider>
        ),
      }
    );

    await act(async () => {
      await hookResult.result.current.copyFieldEvents(CopyFieldEventsMode.Copy);
    });

    expect(onComplete).not.toHaveBeenCalled();
  });

  it('should refetch field events on success', async () => {
    const onComplete = jest.fn();
    const hookResult = renderHook(
      () =>
        useCopyFieldEvents({
          selectedFieldIds: [mockFieldId],
          onComplete,
        }),
      {
        wrapper: ({children}) => (
          <MockedProvider mocks={[BULK_COPY_FIELD_EVENTS_SUCCESS_MOCK]} addTypename={false}>
            <SimpleProviders>{React.isValidElement(children) ? children : <></>}</SimpleProviders>
          </MockedProvider>
        ),
      }
    );

    await act(async () => {
      await hookResult.result.current.copyFieldEvents(CopyFieldEventsMode.Copy);
    });

    await waitFor(() => {
      expect(mockRefetchFieldEvents).toHaveBeenCalled();
    });
  });
});
