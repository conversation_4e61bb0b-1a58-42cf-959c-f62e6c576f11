import {MockedProvider} from '@apollo/client/testing';
import {screen, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';

import {renderWithSimpleProviders} from '_common/test_utils/renderWithProviders';

import {mockSelectedFieldContext} from 'views/mrv/project/phases/stages/__mocks__/SelectedFieldContext';
import {mockProjectFarms} from 'views/mrv/project/phases/stages/__tests__/mock-data/mocks';
import {CopyFieldEvents} from 'views/mrv/project/phases/stages/field-events/update-events/copy-field-events/CopyFieldEventsDialog';
import {useSelectedFieldContext} from 'views/mrv/project/phases/stages/SelectedFieldContext';

import {
  BULK_COPY_FIELD_EVENTS_OVERWRITE_MOCK,
  BULK_COPY_FIELD_EVENTS_SUCCESS_MOCK,
  mockSelectedFieldEvents,
} from './mockData';

jest.mock('views/mrv/project/phases/stages/SelectedFieldContext');
jest.mock('views/mrv/project/phases/PhaseContext');
jest.mock('views/mrv/project/phases/stages/StageContext');
jest.mock('store/useRedux');

jest.mock('views/mrv/project/phases/PhaseContext');
jest.mock('views/mrv/project/phases/stages/StageContext');
jest.mock('store/useRedux');
jest.mock('_common/hooks/use-parsed-match-params', () => ({
  useParsedMatchParams: jest.fn().mockReturnValue({
    projectId: '1',
  }),
}));
jest.mock('views/mrv/project/phases/stages/field-events/utils/refetchFieldEvents');
jest.mock('_common/components/NotificationSnackbar', () => ({
  showNotification: jest.fn(),
}));

describe('CopyFieldEventsDialog', () => {
  const mockUseSelectedFieldContext = useSelectedFieldContext as jest.MockedFunction<
    typeof useSelectedFieldContext
  >;

  beforeEach(() => {
    mockUseSelectedFieldContext.mockReturnValue({
      ...mockSelectedFieldContext,
      farms: mockProjectFarms,
      selectedFieldEvents: mockSelectedFieldEvents,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render the trigger button', () => {
    renderWithSimpleProviders(
      <MockedProvider mocks={[BULK_COPY_FIELD_EVENTS_SUCCESS_MOCK]} addTypename={false}>
        <CopyFieldEvents />
      </MockedProvider>
    );

    expect(screen.getByRole('button', {name: /copy to/i})).toBeInTheDocument();
  });

  it('should open dialog when trigger button is clicked', async () => {
    const user = userEvent.setup();
    renderWithSimpleProviders(
      <MockedProvider mocks={[BULK_COPY_FIELD_EVENTS_SUCCESS_MOCK]} addTypename={false}>
        <CopyFieldEvents />
      </MockedProvider>
    );

    await user.click(screen.getByRole('button', {name: /copy to/i}));

    expect(screen.getByRole('dialog')).toBeInTheDocument();
    expect(screen.getByText('Copy events to fields')).toBeInTheDocument();
  });

  it('should show SelectFieldsToCopy component by default', async () => {
    const user = userEvent.setup();
    renderWithSimpleProviders(
      <MockedProvider mocks={[BULK_COPY_FIELD_EVENTS_SUCCESS_MOCK]} addTypename={false}>
        <CopyFieldEvents />
      </MockedProvider>
    );

    await user.click(screen.getByRole('button', {name: /copy to/i}));

    expect(screen.getByText('Select fields to copy events to.')).toBeInTheDocument();
  });

  it('should navigate to confirm step when fields are selected next is clicked and then back on click', async () => {
    const user = userEvent.setup();
    renderWithSimpleProviders(
      <MockedProvider mocks={[BULK_COPY_FIELD_EVENTS_SUCCESS_MOCK]} addTypename={false}>
        <CopyFieldEvents />
      </MockedProvider>
    );

    await user.click(screen.getByRole('button', {name: /copy to/i}));
    await user.click(screen.getByRole('button', {name: 'Open'}));
    await waitFor(() => {
      expect(screen.getByRole('listbox')).toBeInTheDocument();
    });

    const farm = mockProjectFarms[0];
    await user.click(screen.getByText(String(farm?.name)));
    const fieldOption = screen.getByRole('option', {
      name: 'Field 2 (#mock-field-id)',
    });
    await user.click(fieldOption);

    expect(screen.getByRole('button', {name: 'Copy to fields'})).toBeEnabled();
    await user.click(screen.getByRole('button', {name: 'Copy to fields'}));

    // TODO: MRV-3978 enable once selectedFieldsWithExistingEvents path is implemented
    // await waitFor(() => {
    //   expect(screen.queryByText('Select fields to copy events to.')).not.toBeInTheDocument();
    // });

    // expect(screen.getByText('Confirm')).toBeInTheDocument();

    // await user.click(screen.getByRole('button', {name: 'Back'}));

    // expect(screen.getByText('Select fields to copy events to.')).toBeInTheDocument();
    // expect(screen.queryByText('Confirm')).not.toBeInTheDocument();
  });

  it('should close dialog and reset state when close is called', async () => {
    const user = userEvent.setup();
    renderWithSimpleProviders(
      <MockedProvider mocks={[BULK_COPY_FIELD_EVENTS_SUCCESS_MOCK]} addTypename={false}>
        <CopyFieldEvents />
      </MockedProvider>
    );

    await user.click(screen.getByRole('button', {name: /copy to/i}));
    expect(screen.getByText('Select fields to copy events to.')).toBeInTheDocument();
    expect(screen.getByRole('button', {name: 'Copy to fields'})).toBeDisabled();

    await user.click(screen.getByRole('button', {name: 'Open'}));

    await waitFor(() => {
      expect(screen.getByRole('listbox')).toBeInTheDocument();
    });

    const farm = mockProjectFarms[0];
    await user.click(screen.getByText(String(farm?.name)));
    const fieldOption = screen.getByRole('option', {
      name: 'Field 2 (#mock-field-id)',
    });
    await user.click(fieldOption);

    expect(screen.getByRole('button', {name: 'Copy to fields'})).toBeEnabled();
    await user.click(screen.getByRole('button', {name: 'Cancel'}));

    await waitFor(() => {
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });

    await user.click(screen.getByRole('button', {name: /copy to/i}));
    expect(screen.getByText('Select fields to copy events to.')).toBeInTheDocument();
    expect(screen.getByRole('button', {name: 'Copy to fields'})).toBeDisabled();
  });

  it('should successfully copy field events and close dialog if select fields have no existing events', async () => {
    const user = userEvent.setup();

    // Create a spy to track mutation calls
    const mutationSpy = jest.fn();
    const mockWithSpy = {
      ...BULK_COPY_FIELD_EVENTS_OVERWRITE_MOCK,
      newData: () => {
        mutationSpy();
        return {
          data: BULK_COPY_FIELD_EVENTS_OVERWRITE_MOCK.result?.data,
        };
      },
    };

    renderWithSimpleProviders(
      <MockedProvider mocks={[mockWithSpy, mockWithSpy]} addTypename={false}>
        <CopyFieldEvents />
      </MockedProvider>
    );

    // Open dialog
    await user.click(screen.getByRole('button', {name: /copy to/i}));

    // Select a field
    await user.click(screen.getByRole('button', {name: 'Open'}));
    await waitFor(() => {
      expect(screen.getByRole('listbox')).toBeInTheDocument();
    });

    const farm = mockProjectFarms[0];
    await user.click(screen.getByText(String(farm?.name)));
    const fieldOption = screen.getByRole('option', {
      name: 'Field 2 (#mock-field-id)',
    });
    await user.click(fieldOption);

    expect(screen.getByRole('button', {name: 'Copy to fields'})).toBeEnabled();
    await user.click(screen.getByRole('button', {name: 'Copy to fields'}));

    // Assert that the dialog closes after successful copy
    await waitFor(() => {
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });
  });
});
