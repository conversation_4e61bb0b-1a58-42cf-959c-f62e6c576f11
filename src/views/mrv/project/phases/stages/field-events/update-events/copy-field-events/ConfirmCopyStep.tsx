import React, {useState} from 'react';
import {useIntl} from 'react-intl';

import {
  Box,
  Button,
  DialogActions,
  DialogContent,
  FormControlLabel,
  LoadingButton,
  Radio,
  RadioGroup,
  Typography,
} from '@regrow-internal/design-system';

import {CopyFieldEventsMode} from '__generated__/gql/graphql';

import type {CopyFieldEvents} from './useCopyFieldEvents';

interface FieldWithExistingEvents {
  id: string;
  name: string;
  eventTypes: Array<string>;
}

export const ConfirmCopy = ({
  selectedFieldIds,
  isLoading,
  handlePreviousStep,
  copyFieldEvents,
}: {
  selectedFieldIds: Array<string>;
  isLoading: boolean;
  handlePreviousStep: () => void;
  copyFieldEvents: CopyFieldEvents;
}) => {
  const {formatMessage} = useIntl();

  const [copyMode, setCopyMode] = useState<CopyFieldEventsMode>(CopyFieldEventsMode.Copy);

  const fieldsWithExistingEvents: Array<FieldWithExistingEvents> = [];
  const hasFieldsWithExistingEvents = fieldsWithExistingEvents.length > 0;

  const handleConfirm = () => {
    void copyFieldEvents(copyMode);
  };

  return (
    <>
      <DialogContent>
        {hasFieldsWithExistingEvents && (
          <>
            <Typography variant="h6" sx={{mb: 2}}>
              {formatMessage(
                {
                  id: 'copyFieldEvents.confirm.fieldsWithEvents',
                  defaultMessage: '{count} of your selected fields have existing events.',
                },
                {count: fieldsWithExistingEvents.length}
              )}
            </Typography>

            <Box sx={{mb: 3, pl: 2}}>
              {fieldsWithExistingEvents.map(field => (
                <Box key={field.id} sx={{mb: 0.5}}>
                  <Typography variant="body2" sx={{color: 'warning.main'}}>
                    •&nbsp;
                    {/* {formatMessage(
                      {
                        id: 'copyFieldEvents.confirm.fieldWithEvents',
                        defaultMessage: 'Field {fieldId}: {eventTypes}',
                      },
                      {
                        fieldId: field.id,
                        eventTypes: field.eventTypes.join(', '),
                      }
                    )} */}
                  </Typography>
                </Box>
              ))}
            </Box>

            <RadioGroup
              value={copyMode}
              onChange={({target}) => {
                // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
                setCopyMode(target.value as CopyFieldEventsMode);
              }}
              sx={{mb: 2}}
            >
              <FormControlLabel
                value={CopyFieldEventsMode.Copy}
                control={<Radio />}
                label={
                  <Box>
                    <Typography variant="body2" sx={{fontWeight: 'medium'}}>
                      {formatMessage({
                        id: 'copyFieldEvents.confirm.copySelected',
                        defaultMessage: 'Copy selected events to fields',
                      })}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {formatMessage({
                        id: 'copyFieldEvents.confirm.copySelectedDescription',
                        defaultMessage: 'Existing events will not be overwritten.',
                      })}
                    </Typography>
                  </Box>
                }
              />
              <FormControlLabel
                value={CopyFieldEventsMode.Overwrite}
                control={<Radio />}
                label={
                  <Box>
                    <Typography variant="body2" sx={{fontWeight: 'medium'}}>
                      {formatMessage({
                        id: 'copyFieldEvents.confirm.overwriteExisting',
                        defaultMessage: 'Overwrite existing events with selected events',
                      })}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {formatMessage({
                        id: 'copyFieldEvents.confirm.overwriteExistingDescription',
                        defaultMessage: 'Locked events will not be overwritten.',
                      })}
                    </Typography>
                  </Box>
                }
              />
            </RadioGroup>
          </>
        )}

        {!hasFieldsWithExistingEvents && (
          <Typography variant="body2" color="text.secondary" sx={{mb: 2}}>
            {formatMessage({
              id: 'copyFieldEvents.confirm.noExistingEvents',
              defaultMessage:
                'Selected fields have no existing events. Events will be copied directly.',
            })}
          </Typography>
        )}

        {hasFieldsWithExistingEvents && (
          <Box sx={{mb: 3}}>
            <Typography variant="body2" sx={{mb: 1, fontWeight: 'medium'}}>
              {formatMessage({
                id: 'copyFieldEvents.confirm.scrollableListTitle',
                defaultMessage:
                  'Scrollable list of fields with existing data, and which stages contain existing data',
              })}
            </Typography>
            <Box
              sx={{
                maxHeight: 120,
                overflowY: 'auto',
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 1,
                p: 2,
                backgroundColor: 'grey.50',
              }}
            >
              {fieldsWithExistingEvents.map(field => (
                <Typography key={field.id} variant="body2" sx={{mb: 0.5}}>
                  {field.name} (#{field.id}): {field.eventTypes.join(', ')}
                </Typography>
              ))}
            </Box>
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{justifyContent: 'space-between'}}>
        <Button onClick={handlePreviousStep} color="secondary" variant="outlined">
          {formatMessage({
            id: 'Back',
            defaultMessage: 'Back',
          })}
        </Button>
        <LoadingButton
          onClick={handleConfirm}
          variant="contained"
          disabled={selectedFieldIds.length === 0}
          loading={isLoading}
        >
          {formatMessage({
            id: 'Confirm',
            defaultMessage: 'Confirm',
          })}
        </LoadingButton>
      </DialogActions>
    </>
  );
};
