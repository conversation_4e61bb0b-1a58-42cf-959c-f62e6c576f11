import {getFragmentData} from '__generated__/gql/fragment-masking';
import {
  FieldEventAttributesFragmentDoc,
  FieldWithEventsFragmentDoc,
} from '__generated__/gql/graphql';
import type {GetPhaseFieldCultivationCycleEventsQuery} from '__generated__/gql/graphql';

import type {FieldEventCount} from 'views/mrv/project/phases/stages/field-events/update-events/copy-field-events/useCopyFieldEvents';

/**
 * Processes field event data from all phases and stages to count event types
 * @param phases - a list of MRV phases with stage and event data
 * @returns FieldEventCounts with field information and event type counts
 */

export const processFieldEventCounts = (
  phases:
    | NonNullable<
        NonNullable<GetPhaseFieldCultivationCycleEventsQuery['mrv']['project']>['program']
      >['phases']
    | undefined
): FieldEventCount => {
  const fieldEventCounts: FieldEventCount = {};

  phases?.forEach(phase => {
    const stages = phase?.stages || [];
    stages.forEach(stage => {
      const fieldData = stage?.field;
      if (fieldData) {
        const fieldWithEvents = getFragmentData(FieldWithEventsFragmentDoc, fieldData);

        if (fieldWithEvents?.id) {
          // Initialize field entry if it doesn't exist
          if (!fieldEventCounts[fieldWithEvents.id]) {
            fieldEventCounts[fieldWithEvents.id] = {
              fieldId: fieldWithEvents.id,
              fieldName: fieldWithEvents.name || fieldWithEvents.id,
              eventTypeCounts: {},
            };
          }

          fieldWithEvents?.cultivation_cycles?.forEach(cycle => {
            cycle?.events?.forEach(event => {
              const eventData = getFragmentData(FieldEventAttributesFragmentDoc, event);
              const eventType = eventData?.type;
              if (eventType) {
                const currentField = fieldEventCounts[fieldWithEvents.id];
                currentField.eventTypeCounts[eventType] =
                  (currentField.eventTypeCounts[eventType] || 0) + 1;
              }
            });
          });
        }
      }
    });
  });

  return fieldEventCounts;
};
