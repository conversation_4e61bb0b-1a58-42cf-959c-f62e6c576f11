import {getFragmentData} from '__generated__/gql/fragment-masking';
import {
  FieldEventAttributesFragmentDoc,
  FieldWithEventsFragmentDoc,
} from '__generated__/gql/graphql';
import type {GetPhaseFieldCultivationCycleEventsQuery} from '__generated__/gql/graphql';
import type {StageTypes} from '__generated__/mrv/mrvApi.types';

/**
 * Processes field event data from all phases and stages to count event types
 * @param phases - a list of MRV phases with stage and event data
 * @returns Record of event type counts for a single field
 */

export const processFieldEventCounts = (
  phases:
    | NonNullable<
        NonNullable<GetPhaseFieldCultivationCycleEventsQuery['mrv']['project']>['program']
      >['phases']
    | undefined
): Record<StageTypes, number> => {
  const eventTypeCounts: Record<string, number> = {};

  phases?.forEach(phase => {
    const stages = phase?.stages || [];
    stages.forEach(stage => {
      const fieldData = stage?.field;
      if (fieldData) {
        const fieldWithEvents = getFragmentData(FieldWithEventsFragmentDoc, fieldData);

        fieldWithEvents?.cultivation_cycles?.forEach(cycle => {
          cycle?.events?.forEach(event => {
            const eventData = getFragmentData(FieldEventAttributesFragmentDoc, event);
            const eventType = eventData?.type;
            if (eventType) {
              eventTypeCounts[eventType] = (eventTypeCounts[eventType] || 0) + 1;
            }
          });
        });
      }
    });
  });

  return eventTypeCounts;
};
