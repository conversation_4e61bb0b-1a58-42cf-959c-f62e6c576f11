import type {ReactNode} from 'react';
import React from 'react';

import {Box, Typography, useTheme} from '@regrow-internal/design-system';

import type {Phase} from '__generated__/gql/graphql';
import {PhaseTypes} from '__generated__/mrv/mrvApi.types';

import {IntendedPracticesPrefillAlert} from './components/IntendedPracticesPrefillAlert';
import {MonitorPrefillAlert} from './components/MonitorPrefillAlert';
import {FieldEventsTableWrapper} from './field-event-table/table-components/FieldEventsTableWrapper';
import {FieldEventContextProvider} from './FieldEventContext';

type FieldEventsStageWrapperProps = {
  title: string;
  description: ReactNode;
  children: ReactNode;
  currentPhase: Phase['type'] | undefined;
};

export const FieldEventsStageWrapper: React.FC<FieldEventsStageWrapperProps> = ({
  title,
  description,
  children,
  currentPhase,
}) => {
  const theme = useTheme();

  return (
    <>
      <Box maxWidth={theme.fixedWidths.md}>
        <Typography variant="h2">{title}</Typography>
        <Typography variant="body1">{description}</Typography>
      </Box>
      <Box mt={5}>{currentPhase === PhaseTypes.ENROLMENT && <MonitorPrefillAlert />}</Box>
      <Box mt={5}>
        <FieldEventContextProvider>
          <IntendedPracticesPrefillAlert />
        </FieldEventContextProvider>
      </Box>
      <FieldEventsTableWrapper>{children}</FieldEventsTableWrapper>
    </>
  );
};
