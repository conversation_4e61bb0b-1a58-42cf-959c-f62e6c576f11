import {MockedProvider} from '@apollo/client/testing';
import {screen, waitFor} from '@testing-library/react';
import {enUS} from 'date-fns/locale';
import React from 'react';

import {useAppSelector} from 'store/useRedux';

import {renderWithSimpleProviders} from '_common/test_utils/renderWithProviders';

import {
  GET_PHASE_CULTIVATION_CYCLE_EVENTS_EMPTY_MOCK,
  GET_PHASE_CULTIVATION_CYCLE_EVENTS_MOCK,
} from 'views/mrv/project/phases/stages/__tests__/mock-data/graphqlMocks';
import {FieldEventsTimeline} from 'views/mrv/project/phases/stages/field-events/FieldEventsTimeline';

jest.mock('_common/hooks/use-parsed-match-params', () => ({
  useParsedMatchParams: jest.fn().mockReturnValue({
    projectId: '1',
  }),
}));
jest.mock('views/mrv/project/phases/PhaseContext');
jest.mock('views/mrv/project/phases/stages/StageContext');
jest.mock('views/mrv/project/phases/stages/SelectedFieldContext');
jest.mock('views/mrv/project/phases/stages/field-events/FieldEventContext');

jest.mock('_translations/utils', () => {
  return {
    useDateFnsLocale: jest.fn(() => enUS),
  };
});

jest.mock('containers/mrv/_hooks/useFeatures', () => {
  return {
    ...jest.requireActual('containers/mrv/_hooks/useFeatures'),
    useFeatureEnabled: jest.fn().mockReturnValue(false),
  };
});

jest.mock('store/useRedux', () => ({
  ...jest.requireActual('store/useRedux'),
  useAppSelector: jest.fn(),
}));

(useAppSelector as jest.Mock).mockImplementation(selector =>
  selector({
    integrations: {
      platforms: {
        monitor: {
          syncStatus: 'Idle',
          error: null,
          prefilledFields: [],
        },
      },
    },
  })
);

describe('FieldEventsTimeline', () => {
  it('should display an empty message when no events are present', async () => {
    renderWithSimpleProviders(
      <MockedProvider mocks={[GET_PHASE_CULTIVATION_CYCLE_EVENTS_EMPTY_MOCK]}>
        <FieldEventsTimeline />
      </MockedProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('Fill out the table below to get started.')).toBeInTheDocument();
    });
  });

  it('should display an error message when query returns error and no data', async () => {
    renderWithSimpleProviders(
      <MockedProvider
        mocks={[
          {
            ...GET_PHASE_CULTIVATION_CYCLE_EVENTS_MOCK,
            error: new Error('An error occurred'),
            result: undefined,
          },
        ]}
      >
        <FieldEventsTimeline />
      </MockedProvider>
    );
    await waitFor(() => {
      expect(
        screen.getByText('There was an issue loading your timeline events.')
      ).toBeInTheDocument();
    });
  });

  it('should display events for enabled event stages', async () => {
    renderWithSimpleProviders(
      <MockedProvider mocks={[GET_PHASE_CULTIVATION_CYCLE_EVENTS_MOCK]}>
        <FieldEventsTimeline />
      </MockedProvider>
    );
    await waitFor(() => {
      expect(screen.getByText('Crops')).toBeInTheDocument();
      expect(screen.getByText('Tillage')).toBeInTheDocument();
    });
  });

  it('should display timeline in a loading state when monitor prefill is loading', () => {
    (useAppSelector as jest.Mock).mockImplementation(selector =>
      selector({
        integrations: {
          platforms: {
            monitor: {
              syncStatus: 'Loading',
              error: null,
              prefilledFields: [],
            },
          },
        },
      })
    );
    renderWithSimpleProviders(
      <MockedProvider mocks={[GET_PHASE_CULTIVATION_CYCLE_EVENTS_MOCK]}>
        <FieldEventsTimeline />
      </MockedProvider>
    );

    expect(screen.queryByText('Crops')).not.toBeInTheDocument();
  });

  it('should display timeline in a loading state when monitor prefill field status is loading', () => {
    (useAppSelector as jest.Mock).mockImplementation(selector =>
      selector({
        integrations: {
          platforms: {
            monitor: {
              syncStatus: 'Success',
              error: null,
              prefilledFields: [{md5: 'md5', status: 'processing'}],
            },
          },
        },
      })
    );
    renderWithSimpleProviders(
      <MockedProvider mocks={[GET_PHASE_CULTIVATION_CYCLE_EVENTS_MOCK]}>
        <FieldEventsTimeline />
      </MockedProvider>
    );

    expect(screen.queryByText('Crops')).not.toBeInTheDocument();
  });

  it('should display timeline in view state when monitor prefill field status = succeeded', async () => {
    (useAppSelector as jest.Mock).mockImplementation(selector =>
      selector({
        integrations: {
          platforms: {
            monitor: {
              syncStatus: 'Success',
              error: null,
              prefilledFields: [{md5: 'md5', status: 'succeeded'}],
            },
          },
        },
      })
    );
    renderWithSimpleProviders(
      <MockedProvider mocks={[GET_PHASE_CULTIVATION_CYCLE_EVENTS_MOCK]}>
        <FieldEventsTimeline />
      </MockedProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('Crops')).toBeInTheDocument();
      expect(screen.getByText('Tillage')).toBeInTheDocument();
    });
  });
});
