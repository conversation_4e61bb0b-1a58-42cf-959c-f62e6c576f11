import {useMutation} from '@apollo/client';
import React, {useCallback, useMemo} from 'react';
import {useIntl} from 'react-intl';

import {useAppDispatch} from 'store/useRedux';

import type {
  FieldEventDeleteInput,
  UpdateCultivationCycleNoPracticeObservationMutation,
} from '__generated__/gql/graphql';
import {showNotification} from '_common/components/NotificationSnackbar';
import {useParsedMatchParams} from '_common/hooks/use-parsed-match-params';
import {isEmptyObject} from '_common/utils/typeGuards';

import {
  BULK_CREATE_OR_UPDATE_FIELD_EVENTS,
  CREATE_OR_UPDATE_FIELD_EVENT,
  DELETE_FIELD_EVENT,
  UPDATE_NO_PRACTICE_OBSERVATION,
} from 'views/mrv/graphql/mutations/field-events';
import {usePhaseContext} from 'views/mrv/project/phases/PhaseContext';
import {useStageContext} from 'views/mrv/project/phases/stages/StageContext';

import type {FieldEventRowModel} from './field-event-table/table-components/types';
import {ERROR_MESSAGE_RES} from './types';
import {
  canSaveFieldEvent,
  getCreateUpdateFieldEventInput,
  getEventTypeFromStageType,
  getFilteredEventValues,
} from './utils/fieldEventUtils';
import {refetchFieldEvents} from './utils/refetchFieldEvents';

export type UpdateNoPracticeObservation = (
  cultivationCycleId: string,
  noPracticeObservationValue: boolean
) => Promise<UpdateCultivationCycleNoPracticeObservationMutation>;

const knownErrorMessage = (message: string): boolean => {
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  return Object.values(ERROR_MESSAGE_RES).includes(message as ERROR_MESSAGE_RES);
};

export const useFieldEventActions = (selectedFieldId: string | null) => {
  const intl = useIntl();
  const dispatch = useAppDispatch();

  const {projectId} = useParsedMatchParams<{projectId: string}>();
  const {currentStage} = useStageContext();
  const {currentPhase} = usePhaseContext();
  const phaseId = String(currentPhase?.id);
  const stageId = String(currentStage?.id);

  const refetchEvents = useMemo(
    () => refetchFieldEvents(dispatch, projectId, stageId),
    [dispatch, projectId, stageId]
  );

  const showStatusErrorNotification = useCallback(
    e => {
      showNotification({
        title: intl.formatMessage({
          id: `Errors.CreateUpdateDeleteTitle.${e.message}`,
          defaultMessage: 'There was an issue with your request',
        }),
        message: intl.formatMessage({
          id: `Errors.CreateUpdateDeleteMessage.${e.message}`,
          defaultMessage: 'An unexpected error occurred. Please try again.',
        }),
        type: 'error',
        coloredNotification: true,
      });
    },
    [intl]
  );

  const [deleteEventMutation, {loading: deleteLoading, error: deleteError}] = useMutation(
    DELETE_FIELD_EVENT,
    {
      onError: e => {
        if (knownErrorMessage(e.message)) {
          showStatusErrorNotification(e);
          return;
        }

        showNotification({
          message: intl.formatMessage({
            id: 'Errors.DeleteEvent',
            defaultMessage: 'There was an issue deleting your event',
          }),
          type: 'error',
        });
      },
      ...refetchEvents,
    }
  );

  const deleteFieldEvent = useCallback(
    async (fieldId: string, row: FieldEventRowModel) => {
      const fieldEvent: FieldEventDeleteInput = {
        id: row.id,
        type: row.meta.type,
        event_values: getFilteredEventValues(row),
      };

      return deleteEventMutation({
        variables: {
          projectId,
          phaseId,
          fieldId,
          event: fieldEvent,
        },
      });
    },
    [deleteEventMutation, phaseId, projectId]
  );

  const [
    updateNoPracticeObservationMutation,
    {loading: updateNoPracticeObservationLoading, error: noPracticeError},
  ] = useMutation(UPDATE_NO_PRACTICE_OBSERVATION, {
    onError: e => {
      if (knownErrorMessage(e.message)) {
        showStatusErrorNotification(e);
        return;
      }

      showNotification({
        message: intl.formatMessage({
          id: 'Errors.UpdateNoPracticeObservations',
          defaultMessage: 'There was an issue updating your cultivation cycle practices',
        }),
        type: 'error',
      });
    },
    ...refetchEvents,
  });

  const updateNoPracticeObservation: UpdateNoPracticeObservation = useCallback(
    async (cultivationCycleId: string, noPracticeObservationValue: boolean) => {
      if (!selectedFieldId) return Promise.reject('No field selected');
      const {data, errors} = await updateNoPracticeObservationMutation({
        variables: {
          projectId,
          phaseId,
          stageId,
          fieldId: selectedFieldId,
          cultivationCycleId,
          noPracticeObservationValue,
        },
      });

      if (errors || !data) {
        return Promise.reject('Error updating no practice observation');
      }

      return Promise.resolve(data);
    },
    [phaseId, projectId, selectedFieldId, stageId, updateNoPracticeObservationMutation]
  );

  const [
    bulkCreateOrUpdateEventsMutation,
    {loading: bulkCreateOrUpdateLoading, error: bulkCreateUpdateError},
  ] = useMutation(BULK_CREATE_OR_UPDATE_FIELD_EVENTS, {
    onError: e => {
      if (knownErrorMessage(e.message)) {
        showStatusErrorNotification(e);
        return;
      }

      showNotification({
        message: intl.formatMessage({
          id: 'Errors.BulkCreateUpdateEvents',
          defaultMessage: 'There was an issue updating your events',
        }),
        type: 'error',
      });
    },
    ...refetchEvents,
  });

  const bulkCreateOrUpdateEvents = useCallback(
    async (events: Array<FieldEventRowModel>) => {
      if (!stageId || !currentStage?.type) return Promise.reject('Stage not found');
      if (!selectedFieldId) return Promise.reject('No field selected');

      const bulkEventPayload = events.map(fieldEvent => {
        const eventType =
          getEventTypeFromStageType(String(currentStage?.type)) || fieldEvent.meta.type;
        const event = getCreateUpdateFieldEventInput(fieldEvent, eventType);

        return {
          type: event.type,
          fieldId: selectedFieldId,
          phaseId,
          projectId,
          id: event.id,
          event_values: {
            ...event.event_values,
          },
        };
      });

      return bulkCreateOrUpdateEventsMutation({
        variables: {
          events: bulkEventPayload,
        },
      });
    },
    [
      bulkCreateOrUpdateEventsMutation,
      phaseId,
      projectId,
      currentStage?.type,
      selectedFieldId,
      stageId,
    ]
  );

  const [createOrUpdateEventMutation, {loading: createOrUpdateLoading, error: createUpdateError}] =
    useMutation(CREATE_OR_UPDATE_FIELD_EVENT, {
      onError: (e, clientOptions) => {
        let validation_errors = {};
        let eventType;

        if (knownErrorMessage(e.message)) {
          showStatusErrorNotification(e);
          return;
        }

        try {
          const parsedMessage = JSON.parse(e.message);
          validation_errors = parsedMessage.validation_errors ?? {};
          eventType = clientOptions?.variables?.event?.type ?? 'Event';
        } catch (err) {
          showNotification({
            title: intl.formatMessage({
              id: 'Errors.CreateUpdateEventTitle.Generic',
              defaultMessage: 'There was an issue updating your event',
            }),
            message: intl.formatMessage({
              id: 'Errors.CreateUpdateEventMessage.Generic',
              defaultMessage: 'An unexpected error occurred. Please try again.',
            }),
            type: 'error',
            coloredNotification: true,
          });
          return;
        }

        const eventTypeCleaned = eventType.replace(/Event$/, '');

        const validationErrorElements = Object.entries(validation_errors).map(([key, value]) => {
          const formattedMessage = intl.formatMessage({
            id: `Stage error: ${value}`,
            defaultMessage: `${value}`,
          });

          return <div key={key}>{formattedMessage}</div>;
        });

        showNotification({
          title: intl.formatMessage({
            id: `Errors.CreateUpdateEventTitle.${eventTypeCleaned}`,
            defaultMessage: `There was an issue updating your ${eventTypeCleaned} event`,
          }),
          message: <div>{validationErrorElements}</div>,
          type: 'error',
          coloredNotification: true,
        });
      },
      ...refetchEvents,
    });

  const createOrUpdateEvent = useCallback(
    async (fieldEvent: FieldEventRowModel) => {
      if (!stageId || !currentStage?.type) return Promise.reject('Stage not found');
      if (!selectedFieldId) return Promise.reject('No field selected');

      if (isEmptyObject(fieldEvent) || !fieldEvent) {
        return Promise.reject('No event data provided');
      }

      const eventType =
        getEventTypeFromStageType(String(currentStage?.type)) || fieldEvent.meta.type;

      if (!canSaveFieldEvent(eventType, fieldEvent)) {
        return Promise.reject('Missing required event data');
      }

      const event = getCreateUpdateFieldEventInput(fieldEvent, eventType);

      return createOrUpdateEventMutation({
        variables: {
          fieldId: selectedFieldId,
          phaseId,
          projectId,
          event,
        },
      });
    },
    [createOrUpdateEventMutation, currentStage?.type, phaseId, projectId, selectedFieldId, stageId]
  );

  return {
    createOrUpdateEvent,
    createOrUpdateLoading,
    deleteFieldEvent,
    deleteLoading,
    updateNoPracticeObservation,
    updateNoPracticeObservationLoading,
    updateFieldEventsError: deleteError || noPracticeError || createUpdateError,
    bulkCreateOrUpdateEvents,
    bulkCreateOrUpdateLoading,
    bulkCreateUpdateError,
  };
};
