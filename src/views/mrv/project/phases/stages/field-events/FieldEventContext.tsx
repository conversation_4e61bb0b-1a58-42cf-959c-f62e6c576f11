import type {FetchResult} from '@apollo/client';
import {NetworkStatus, useQuery} from '@apollo/client';
import React, {createContext, useContext, useEffect, useMemo} from 'react';
import type {FC} from 'react';

import {getFragmentData} from '__generated__/gql/fragment-masking';
import type {
  BulkCreateOrUpdateFieldEventsMutation,
  CreateOrUpdateFieldEventMutation,
  DeleteFieldEventMutation,
  GetProjectFarmsAndEnrolledFieldsQuery,
} from '__generated__/gql/graphql';
import {
  FieldEventAttributesFragmentDoc,
  FieldWithEventsFragmentDoc,
} from '__generated__/gql/graphql';
import {useParsedMatchParams} from '_common/hooks/use-parsed-match-params';
import {isDefined} from '_common/utils/typeGuards';

import {useFeatureEnabled} from 'containers/mrv/_hooks/useFeatures';
import {FEATURE_FLAGS} from 'containers/mrv/constants';
import {useMonitorPrefillImportFieldStatus} from 'containers/profile/fms-integration/hooks';
import {GET_FIELD_CULTIVATION_CYCLE_EVENTS} from 'views/mrv/graphql/queries/field-events';
import {useSelectedFieldContext} from 'views/mrv/project/phases/stages/SelectedFieldContext';
import {useStageContext} from 'views/mrv/project/phases/stages/StageContext';

import type {FieldEventRowModel} from './field-event-table/table-components/types';
import type {UpdateNoPracticeObservation} from './useFieldEventActions';
import {useFieldEventActions} from './useFieldEventActions';

export type FarmsQueryResponse = NonNullable<
  NonNullable<GetProjectFarmsAndEnrolledFieldsQuery['mrv']['project']>['farms']
>;
export type FieldQueryResponse = NonNullable<NonNullable<FarmsQueryResponse[0]>['fields']>[0];

const FieldWithEventsFragment = getFragmentData(FieldWithEventsFragmentDoc, {});
const FieldEventsFragment = getFragmentData(FieldEventAttributesFragmentDoc, {});

export type FieldCultivationCyclesResponse = (typeof FieldWithEventsFragment)['cultivation_cycles'];
export type FieldEvent = NonNullable<typeof FieldEventsFragment>;

export type FieldCultivationCycle = Omit<
  NonNullable<FieldCultivationCyclesResponse>[0],
  'events'
> & {
  events?: Array<FieldEvent> | null;
};

type CreateOrUpdateField = (
  fieldEvent: FieldEventRowModel
) => Promise<FetchResult<CreateOrUpdateFieldEventMutation>>;

export type BulkCreateOrUpdateFieldEvents = (
  events: Array<FieldEventRowModel>
) => Promise<FetchResult<BulkCreateOrUpdateFieldEventsMutation>>;

export type FieldEventContextType = {
  updateFieldEventsLoading: boolean;
  updateFieldEventsError: string | null;
  error: string | null;
  isLoading: boolean;
  fieldCultivationCycles: Array<FieldCultivationCycle>;
  createOrUpdateEvent: CreateOrUpdateField;
  deleteFieldEvent: (
    fieldId: string,
    event: FieldEventRowModel
  ) => Promise<FetchResult<DeleteFieldEventMutation>>;
  updateNoPracticeObservation: UpdateNoPracticeObservation;
  bulkCreateOrUpdateEvents: BulkCreateOrUpdateFieldEvents;
  bulkCreateOrUpdateLoading: boolean;
};

const FieldEventContext = createContext<FieldEventContextType>({
  createOrUpdateEvent: async () => Promise.resolve({}),
  updateFieldEventsLoading: false,
  updateFieldEventsError: null,
  deleteFieldEvent: async () => Promise.resolve({}),
  error: null,
  isLoading: true,
  fieldCultivationCycles: [],
  updateNoPracticeObservation: async () => Promise.resolve({}),
  bulkCreateOrUpdateEvents: async () => Promise.resolve({}),
  bulkCreateOrUpdateLoading: false,
});

export const FieldEventContextProvider: FC = ({children}) => {
  const {projectId} = useParsedMatchParams<{projectId: string}>();
  const {currentStage} = useStageContext();
  const {selectedField} = useSelectedFieldContext();
  const selectedFieldId = String(selectedField?.id);
  const {monitorImportIsComplete, isMonitorSyncLoading} = useMonitorPrefillImportFieldStatus();
  const pollForMonitorEvents = isMonitorSyncLoading && !monitorImportIsComplete;
  const isIntendedPrefillEnabled = useFeatureEnabled(FEATURE_FLAGS.INTENDED_PRACTICES_PREFILL);

  // Single CC events query
  const {
    data: getCultivationCyclesForField,
    error: cultivationCyclesError,
    networkStatus: cultivationCyclesLoading,
    refetch: refetchCultivationCycleEvents,
  } = useQuery(GET_FIELD_CULTIVATION_CYCLE_EVENTS, {
    variables: {
      fieldId: String(selectedFieldId),
      projectId,
      stageId: String(currentStage?.id),
      prefill_monitoring_phase: isIntendedPrefillEnabled,
    },
    skip: !currentStage?.id || !isDefined(selectedFieldId),
    fetchPolicy: 'network-only',
    nextFetchPolicy: 'cache-and-network',
    refetchWritePolicy: 'overwrite',
    errorPolicy: 'all',
    pollInterval: pollForMonitorEvents ? 5000 : 0,
  });

  useEffect(() => {
    const refreshData = async () => {
      await refetchCultivationCycleEvents();
    };

    if (monitorImportIsComplete && !isMonitorSyncLoading) {
      void refreshData();
    }
  }, [isMonitorSyncLoading, monitorImportIsComplete, refetchCultivationCycleEvents]);

  const fieldsDataLoading =
    !currentStage ||
    (cultivationCyclesLoading < NetworkStatus.ready && !getCultivationCyclesForField);

  const fieldCultivationCycles = useMemo(() => {
    const fieldWithEvents = getFragmentData(
      FieldWithEventsFragmentDoc,
      getCultivationCyclesForField?.mrv?.project?.stage?.field
    );

    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    return (
      fieldWithEvents?.cultivation_cycles ? fieldWithEvents.cultivation_cycles : []
    ) as Array<FieldCultivationCycle>;
  }, [getCultivationCyclesForField?.mrv?.project?.stage?.field]);

  const {
    createOrUpdateEvent,
    createOrUpdateLoading,
    deleteFieldEvent,
    deleteLoading,
    updateNoPracticeObservation,
    updateNoPracticeObservationLoading,
    updateFieldEventsError,
    bulkCreateOrUpdateEvents,
    bulkCreateOrUpdateLoading,
  } = useFieldEventActions(selectedFieldId);

  const updateFieldEventsLoading =
    createOrUpdateLoading ||
    deleteLoading ||
    updateNoPracticeObservationLoading ||
    cultivationCyclesLoading === NetworkStatus.refetch;

  const fieldEventContext = useMemo(
    () => ({
      createOrUpdateEvent,
      updateFieldEventsLoading,
      updateFieldEventsError: updateFieldEventsError ? updateFieldEventsError.message : null,
      deleteFieldEvent,
      error: cultivationCyclesError ? cultivationCyclesError.message : null,
      isLoading: fieldsDataLoading,
      fieldCultivationCycles,
      updateNoPracticeObservation,
      bulkCreateOrUpdateEvents,
      bulkCreateOrUpdateLoading,
    }),
    [
      createOrUpdateEvent,
      updateFieldEventsLoading,
      updateFieldEventsError,
      deleteFieldEvent,
      cultivationCyclesError,
      fieldsDataLoading,
      fieldCultivationCycles,
      updateNoPracticeObservation,
      bulkCreateOrUpdateEvents,
      bulkCreateOrUpdateLoading,
    ]
  );

  return (
    <FieldEventContext.Provider value={fieldEventContext}>{children}</FieldEventContext.Provider>
  );
};

export const useFieldEventContext = () => {
  return useContext<FieldEventContextType>(FieldEventContext);
};
