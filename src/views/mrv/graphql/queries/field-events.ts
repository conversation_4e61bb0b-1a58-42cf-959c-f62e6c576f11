import {gql} from '__generated__/gql';

gql(/* GraphQL */ `
  fragment CroppingEventAttributes on CroppingEvent {
    planting_date
    harvest_date
    crop_type
    crop_yield
    yield_rate_unit
    termination_method
    residue_harvested
    crop_usage
  }

  fragment FallowPeriodAttributes on FallowPeriod {
    start_date
    end_date
    crop_type
  }

  fragment TillageEventAttributes on TillageEvent {
    tillage_practice
    tillage_date
    tillage_depth
    tillage_depth_unit
    soil_inversion
  }

  fragment IrrigationEventAttributes on IrrigationEvent {
    start_date
    end_date
    subsurface_drip_depth
    subsurface_drip_depth_unit
    flood_pct
    irrigation_method
  }

  fragment ApplicationEventAttributes on ApplicationEvent {
    application_product
    application_date
    application_rate
    application_rate_type
    application_rate_unit
    application_method
    application_depth
    application_depth_unit
    water_amount
    water_amount_unit
    additives
  }

  fragment FieldWithEvents on Field {
    id
    cultivation_cycles(prefillMonitoringPhase: $prefill_monitoring_phase) {
      id
      start_date
      end_date
      crop_type
      crop_event_is_locked
      contains_prefilled_monitoring_phase_events
      no_practice_observations {
        tillage_event
        irrigation_event
        application_event
      }
      events {
        ...FieldEventAttributes
      }
    }
  }

  fragment FieldEventAttributes on FieldEvent {
    id
    type
    is_locked
    event_values {
      ...FallowPeriodAttributes
      ...CroppingEventAttributes
      ...TillageEventAttributes
      ...ApplicationEventAttributes
      ...IrrigationEventAttributes
    }
  }
`);

export const GET_FIELD_CULTIVATION_CYCLE_EVENTS = gql(/* GraphQL */ `
  query getStageFieldCultivationCycleEvents(
    $projectId: ID!
    $fieldId: ID!
    $stageId: ID!
    $prefill_monitoring_phase: Boolean!
  ) {
    mrv {
      project(projectId: $projectId) {
        id
        stage(stageId: $stageId) {
          id
          field(fieldId: $fieldId) {
            ...FieldWithEvents
          }
        }
      }
    }
  }
`);

export const GET_PHASE_CULTIVATION_CYCLE_EVENTS = gql(/* GraphQL */ `
  query getPhaseFieldCultivationCycleEvents(
    $projectId: ID!
    $fieldId: ID!
    $phaseType: String
    $prefill_monitoring_phase: Boolean!
  ) {
    mrv {
      project(projectId: $projectId) {
        id
        program {
          id
          phases(phaseType: $phaseType) {
            id
            stages {
              id
              type
              field(fieldId: $fieldId) {
                ...FieldWithEvents
              }
            }
          }
        }
      }
    }
  }
`);
