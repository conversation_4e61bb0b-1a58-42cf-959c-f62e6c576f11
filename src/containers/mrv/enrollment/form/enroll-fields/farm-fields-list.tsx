import cn from 'classnames';
import React, {useEffect, useMemo, useState} from 'react';
import {useIntl} from 'react-intl';

import {
  Button,
  Checkbox,
  FormControlLabel,
  SvgIcon,
  Typography,
} from '@regrow-internal/design-system';

import {useAppDispatch, useAppSelector} from 'store/useRedux';

import {useParsedMatchParams} from '_common/hooks/use-parsed-match-params';
import {selectProtectedAreaBoundaries} from '_common/modules/add-fields/selectors';
import {removeFarm} from '_common/modules/farms/actions';
import {selectFarmsList} from '_common/modules/farms/selectors';
import {
  selectAllFieldsList,
  selectCurrentFarm,
  selectFieldGeometries,
  selectFieldsByFarmId,
  selectHighlightedFieldId,
} from '_common/modules/global/map-selectors';
import {dialogToggle, DialogType} from '_common/modules/helpers';
import {convertUnit} from '_common/utils/conversions';
import {MeasurementEnum} from '_common/utils/measurement-unit-options';
import {toFixedFloatUnsafe} from '_common/utils/number-formatters';
import {doesObjectHaveProperties} from '_common/utils/object';
import {isDefined} from '_common/utils/typeGuards';

import {
  selectIsUserSuperAdminOrImpersonatorIsSuperAdmin,
  selectMeasurement,
  selectUserIsImpersonated,
} from 'containers/login/login-selectors';
import {useFeatureEnabled} from 'containers/mrv/_hooks/useFeatures';
import {useHasPermissions} from 'containers/mrv/_hooks/useHasPermissions';
import {usePhaseReadOnly} from 'containers/mrv/_hooks/usePhaseReadOnly';
import {FEATURE_FLAGS} from 'containers/mrv/constants';
import type {BoundaryViolationsByFieldId} from 'containers/mrv/enrollment/boundary-violations-context';
import {enrollFields} from 'containers/mrv/enrollment/carbon-store/carbon-thunks';
import {selectHistoricPracticesStage} from 'containers/mrv/monitoring/module/enrollment.selectors';
import {
  selectCurrentProjectFarms,
  selectCurrentProjectMRVEnrolledFieldsByKmlId,
  selectStageFieldValuesMap,
} from 'containers/mrv/monitoring/module/selectors';
import {deleteFarm, removeMRVFields} from 'containers/mrv/monitoring/module/thunks';
import {EPermissions, MRVPhaseType} from 'containers/mrv/types';
import {toggleFieldsInsideFarm} from 'containers/mrv/utils';
import type {Farm} from 'containers/shared/types';
import {getFarmById, sortFieldsByProp} from 'containers/shared/utils/farm-utils';

import {carbonActions} from '../../carbon-store/carbon-reducer';
import {ActionsButton} from './base';
import {FarmFieldsEditDialog} from './farm-fields-edit-dialog';
import {EnrollFieldsFieldItem} from './field-item';

type FarmFieldsListProps = {
  boundaryViolationsByFieldId?: BoundaryViolationsByFieldId;
};
export const FarmFieldsList = ({boundaryViolationsByFieldId = {}}: FarmFieldsListProps) => {
  const dispatch = useAppDispatch();
  const carbon = useAppSelector(s => s.carbon);
  // This is our redux store state, it's not actually enrolled, it is used for selection.
  const selectedFieldsByKmlId = carbon.enrolledFields;
  const mrvEnrolledFieldsByKmlId = useAppSelector(selectCurrentProjectMRVEnrolledFieldsByKmlId);
  const fieldsByFarmId = useAppSelector(selectFieldsByFarmId);
  const farms = useAppSelector(selectFarmsList);
  const mrvFarms = useAppSelector(selectCurrentProjectFarms);
  const currentFarm = useAppSelector(selectCurrentFarm);
  const measurement = useAppSelector(selectMeasurement);
  const fieldGeometries = useAppSelector(selectFieldGeometries);
  const allFields = useAppSelector(selectAllFieldsList);
  const {projectId} = useParsedMatchParams();
  const {isReadOnly} = usePhaseReadOnly(MRVPhaseType.Enrolment);
  const highlightedFieldId = useAppSelector(selectHighlightedFieldId);
  const protectedAreaBoundaries = useAppSelector(selectProtectedAreaBoundaries);
  const [canCreateProjectFields, canDeleteProjectFields, canDeleteProjectFarms] = useHasPermissions(
    [
      EPermissions.CREATE_PROJECT_FIELDS,
      EPermissions.DELETE_PROJECT_FIELDS,
      EPermissions.DELETE_PROJECT_FARMS,
    ]
  );

  const userIsImpersonated = useAppSelector(selectUserIsImpersonated);
  const isAdmin = useAppSelector(selectIsUserSuperAdminOrImpersonatorIsSuperAdmin);
  const farmFieldDeletionEnabled = useFeatureEnabled(FEATURE_FLAGS.FARM_FIELD_DELETION_ENABLED);

  const shouldShowDeleteFarmButton =
    (isAdmin || userIsImpersonated) &&
    canDeleteProjectFields &&
    canDeleteProjectFarms &&
    (isAdmin || farmFieldDeletionEnabled);

  const shouldShowDeleteFieldButton =
    (isAdmin || userIsImpersonated) &&
    canDeleteProjectFields &&
    (isAdmin || farmFieldDeletionEnabled);

  const historicPracticesStage = useAppSelector(selectHistoricPracticesStage);
  const historicPracticesPerField = useAppSelector(s =>
    /* Casting hides warnings and errors. It can result in hard to debug code, and unpredictable behaviour. In almost all cases casting isn't needed. */
    /* eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- fixme */
    selectStageFieldValuesMap(s, historicPracticesStage?.id as number)
  );

  const [collapsedFarms, setCollapsedFarms] = useState<{[farmId: number]: boolean}>({});
  const [farmFieldsEditOpen, setFarmFieldsEditOpen] = useState<number | null>(null);

  const intl = useIntl();

  const farmsToProcess = useMemo(() => {
    return Object.keys(fieldsByFarmId || {}).reduce((acc: Farm[], farmKey) => {
      const farmId = Number(farmKey);
      const farm = getFarmById(farms, farmId);

      if (!farm) return acc;

      return [...acc, farm];
    }, []);
  }, [fieldsByFarmId, farms]);

  useEffect(() => {
    if (farmsToProcess && doesObjectHaveProperties(boundaryViolationsByFieldId)) {
      const farmsWithBoundaryViolations = Object.entries(fieldsByFarmId || {}).reduce(
        (farmsWithViolations, [farmId, farmFields]) => {
          if (Object.values(farmFields).some(field => boundaryViolationsByFieldId[field.FieldID])) {
            return {...farmsWithViolations, [farmId]: true};
          }
          return farmsWithViolations;
        },
        {}
      );

      if (doesObjectHaveProperties(farmsWithBoundaryViolations)) {
        setCollapsedFarms(farmsWithBoundaryViolations);
      }
    }
  }, [boundaryViolationsByFieldId, farmsToProcess, fieldsByFarmId]);

  useEffect(() => {
    const eligibility: {[fieldId: number]: boolean} = {};
    allFields.forEach(f => {
      // const geom = fieldGeometries[f.MD5];
      // eligibility[f.ID] = featureCollectionContains(pppx as GeoJSON.FeatureCollection, geom);
      eligibility[f.ID] = true; // pass all fields for now https://regrowag.slack.com/archives/C02EZPYF399/p1642583309083700?thread_ts=1642575495.076600&cid=C02EZPYF399
    });
    dispatch(carbonActions.setEligibleRegionFields(eligibility));
  }, [dispatch, allFields, fieldGeometries]);

  const toggleExpandFarm = (farmId: number, forceValue?: boolean) => {
    setCollapsedFarms(e => ({...e, [farmId]: forceValue !== undefined ? forceValue : !e[farmId]}));
  };

  const farmIsSelected = (farmId: number) => {
    const kmlIds: number[] = [];

    let everyFieldIsSelected = true;

    for (const fID in fieldsByFarmId?.[farmId]) {
      const kmlId = Number(fID);
      const field = fieldsByFarmId[farmId]?.[kmlId];

      if (field?.DeletedAt && !mrvEnrolledFieldsByKmlId[kmlId]) {
        continue;
      }

      if (everyFieldIsSelected && !selectedFieldsByKmlId[kmlId]) {
        everyFieldIsSelected = false;
      }

      kmlIds.push(kmlId);
    }

    return kmlIds.length > 0 && everyFieldIsSelected;
  };

  const toggleFarm = async (farmId: number, forceValue?: boolean) => {
    const value = forceValue !== undefined ? forceValue : !farmIsSelected(farmId);
    const farmFields = fieldsByFarmId?.[farmId];
    const eligibleByKmlId = carbon.eligibleRegionFields;
    const selection = toggleFieldsInsideFarm(farmFields, eligibleByKmlId, value);
    if (value) {
      await dispatch(enrollFields(selection));
      return;
    }

    const hasHistoricPractices = Object.keys(selection)
      .map(Number)
      .some(kmlId => {
        const mrvFieldId = mrvEnrolledFieldsByKmlId[kmlId]?.id;
        return (mrvFieldId ? historicPracticesPerField[mrvFieldId] : [])?.find(
          v => v.value && v.source === 'user'
        );
      });
    if (!hasHistoricPractices) {
      await dispatch(enrollFields(selection));
      return;
    }

    const userAgreesWithHistoricPracticesLoss = confirm(
      intl.formatMessage({
        id: 'If you decide to enroll this farm again, you will need to re-enter its cropping practices.',
        defaultMessage:
          'If you decide to enroll this farm again, you will need to re-enter its cropping practices.',
      })
    );
    if (!userAgreesWithHistoricPracticesLoss) return;
    await dispatch(enrollFields(selection));
  };

  const overlapIds = useMemo(() => new Set(carbon.overlapFields.flat()), [carbon.overlapFields]);

  return (
    <>
      <div className="farm-list">
        {farmsToProcess.map(farm => {
          const farmId = farm.id;
          const availableFields = Object.values(fieldsByFarmId?.[farmId] || {}).filter(
            f => !f.DeletedAt || mrvEnrolledFieldsByKmlId[f.ID]
          );
          const kmlIds = Object.keys(fieldsByFarmId?.[farmId] || {}).map(Number);
          const overlap = kmlIds.some(id => overlapIds.has(id));

          if (!availableFields.length) {
            return null;
          }

          const farmName =
            farms.find(f => f.id === farmId)?.name ||
            (currentFarm?.id === farmId && currentFarm?.name) || // if the list is not there and we're filling the current farm, use it
            '';
          const farmArea = convertUnit(
            measurement,
            MeasurementEnum.ImperialUnits,
            availableFields.reduce((acc, f) => acc + f.Area, 0)
          );

          const classifiedFarmArea =
            farmArea > 1000 ? `${toFixedFloatUnsafe(farmArea / 1000, 1)}k ` : farmArea;

          const selectedFields = availableFields.filter(f => selectedFieldsByKmlId[f.ID]);
          const isFarmSelected = farmIsSelected(farmId);
          const isIneligibleFarm = availableFields.every(f => !carbon.eligibleRegionFields[f.ID]);
          const isExpanded = collapsedFarms[farmId];
          // Don't bother sorting if the farm is not expanded.
          const sortedFields = isExpanded
            ? sortFieldsByProp(availableFields, 'Name', 'string')
            : [];
          const farmActions = [];
          if (canCreateProjectFields) {
            farmActions.push(
              {
                label: intl.formatMessage({id: 'Edit name', defaultMessage: 'Edit name'}),
                onClick: () =>
                  dispatch(
                    dialogToggle(DialogType.editFarmName, true, {
                      farmId,
                      farmName,
                      projectId,
                    })
                  ),
              },
              {
                label: intl.formatMessage({
                  id: 'Move field(s) to another farm',
                  defaultMessage: 'Move field(s) to another farm',
                }),
                onClick: () => setFarmFieldsEditOpen(farmId),
              }
            );
          }
          if (shouldShowDeleteFarmButton) {
            farmActions.push({
              label: intl.formatMessage({id: 'Delete', defaultMessage: 'Delete'}),
              onClick: () =>
                dispatch(
                  dialogToggle(DialogType.deleteDialog, true, {
                    title: intl.formatMessage({
                      id: 'Delete farm?',
                      defaultMessage: 'Delete farm?',
                    }),
                    details: intl.formatMessage({
                      id: 'Before deleting the Farm please ensure that it is not enrolled in another active program phase.',
                      defaultMessage:
                        'Before deleting the Farm please ensure that it is not enrolled in another active program phase.',
                    }),
                    onSubmit: async () => {
                      dispatch(carbonActions.removeKMLsFromRedux({kmlIds}));
                      const mrvFieldIds = kmlIds
                        .map(kmlId => mrvEnrolledFieldsByKmlId[kmlId]?.id)
                        .filter(isDefined);
                      await dispatch(removeMRVFields({projectId, mrvFieldIds, stageId: undefined}));
                      await dispatch(removeFarm(farmId, true));
                      const mrvFarmId = mrvFarms.find(f => f.core_farm_group_id === farmId)?.id;
                      if (isDefined(mrvFarmId)) {
                        void deleteFarm({projectId, mrvFarmId});
                      }
                    },
                  })
                ),
            });
          }

          return (
            <div className={cn('farm', {'farm--expanded': isExpanded})} key={farmId}>
              <div className={cn('farm__row', {overlap})}>
                <div
                  className="farm-checkbox-container ml-05"
                  onClick={() => {
                    // expand farm when click on the master checkbox and if all fields are ineligible
                    if (isIneligibleFarm) {
                      toggleExpandFarm(farmId, true);
                    }
                  }}
                >
                  <FormControlLabel
                    label={
                      <div title={farmName} className={'checkbox-label'}>
                        <Typography variant="h5" variantMapping={{h5: 'body1'}}>
                          {farmName}
                        </Typography>
                        <Typography color="secondary" variant="body2">
                          {classifiedFarmArea}&nbsp;
                          {intl.formatMessage({id: measurement, defaultMessage: measurement})}
                        </Typography>
                      </div>
                    }
                    control={
                      <Checkbox
                        inputProps={{
                          'aria-label': intl.formatMessage(
                            {
                              id: 'Select all fields for {farmName}',
                              defaultMessage: 'Select all fields for {farmName}',
                            },
                            {
                              farmName,
                            }
                          ),
                        }}
                        indeterminate={selectedFields.length > 0 && !isFarmSelected}
                        disabled={isReadOnly || !canCreateProjectFields}
                        checked={!!isFarmSelected}
                        sx={{
                          marginLeft: 1,
                        }}
                        onChange={() =>
                          toggleFarm(farmId, isFarmSelected ? false : !selectedFields.length)
                        }
                      />
                    }
                  />
                </div>

                <Typography variant="body2">
                  {intl.formatMessage(
                    {
                      id: '{count1} / {count2} fields selected',
                      defaultMessage: '{count1} / {count2} fields selected',
                    },
                    {count1: selectedFields.length, count2: availableFields.length}
                  )}
                </Typography>

                <Button
                  aria-label={`${farmName} field list`}
                  aria-controls={`#${farmName}-field-list`}
                  aria-expanded={isExpanded}
                  variant="text"
                  sx={{width: 30, height: 30, ml: 1}}
                  onClick={() => toggleExpandFarm(farmId)}
                  color="secondary"
                >
                  <SvgIcon
                    type={isExpanded ? 'chevron-up' : 'chevron-down'}
                    fontSize="h5"
                    color="secondary"
                  />
                </Button>

                <ActionsButton
                  label={`${farmName}-farm-menu-action-button`}
                  disabled={isReadOnly || !canCreateProjectFields}
                  actions={farmActions}
                />
              </div>
              {isExpanded && (
                <div id={`${farmName}-field-list`}>
                  {sortedFields.map(field => {
                    const isKMLFieldDeleted = !!allFields.find(f => f.ID === field.ID)?.DeletedAt;

                    const selected = !!selectedFieldsByKmlId[field.ID];
                    const mrvEnrolledField = mrvEnrolledFieldsByKmlId[field.ID];

                    if (isKMLFieldDeleted && !mrvEnrolledField) {
                      return null;
                    }

                    const geometry = fieldGeometries?.[field.MD5];

                    const editable =
                      !field.Seasons?.length && !field.external_service && canCreateProjectFields;
                    const highlighted = highlightedFieldId === field.ID;
                    const isOverlap = overlapIds.has(field.ID);
                    const ineligible = !!carbon.ineligibleRegionFields[field.FieldID];

                    const checkboxIsDisabled =
                      isReadOnly ||
                      !carbon.eligibleRegionFields[field.ID] ||
                      !canCreateProjectFields;

                    const hasProtectedArea = geometry?.features.some(
                      // TODO: is this supposed to be checking f or is using field correct?
                      // if it's supposed to be checking `field`, why use `some`?
                      // eslint-disable-next-line @typescript-eslint/no-unused-vars
                      f => !!protectedAreaBoundaries[field.MD5]
                    );

                    const fieldArea = `${convertUnit(
                      measurement,
                      MeasurementEnum.ImperialUnits,
                      field.Area
                    )} ${intl.formatMessage({
                      id: measurement,
                      defaultMessage: measurement,
                    })}`;

                    return (
                      <EnrollFieldsFieldItem
                        canCreateFields={!!canCreateProjectFields}
                        canDeleteFields={!!shouldShowDeleteFieldButton}
                        checkboxIsDisabled={checkboxIsDisabled}
                        editable={!!editable}
                        selected={selected}
                        farmId={farm.id}
                        field={field}
                        fieldArea={fieldArea}
                        geometry={geometry}
                        hasProtectedArea={hasProtectedArea}
                        boundaryViolations={boundaryViolationsByFieldId[field.FieldID]}
                        highlighted={highlighted}
                        ineligible={ineligible}
                        isReadOnly={isReadOnly}
                        key={field.ID}
                        overlap={isOverlap}
                      />
                    );
                  })}
                </div>
              )}
            </div>
          );
        })}
      </div>
      {farmFieldsEditOpen !== null && (
        <FarmFieldsEditDialog
          currentFarmId={farmFieldsEditOpen}
          fieldsByFarmId={fieldsByFarmId}
          onClose={() => {
            setFarmFieldsEditOpen(null);
          }}
        />
      )}
    </>
  );
};
