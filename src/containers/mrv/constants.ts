import {t} from '_translations/t';

import {MRVStageType} from 'containers/mrv/types';

import {EnrollmentStep} from './enrollment/carbon-store/types';

export const MRVPhasesDateFormat = 'MMM D YYYY';

export const MRVEntityAllEntitiesId = -1; // By default show all entities (don't filter).

export const defaultSuperclusterOptions = {
  radius: 60,
  extent: 256,
  maxZoom: 11,
};

export const ENROLLMENT_PHASE = 'ENROLLMENT';

export const PHASE_FINALIZATION_STAGE_TYPE = 'FINISH';

export const PHASE_FINALIZATION_STAGE_ID = -1;

export const BASEMAP_TYPE = {
  MAPBOX: 'mapbox-basemap',
  GOOGLE: 'google-basemap',
};

// INTRODUCED IN https://regrow.atlassian.net/browse/MRV-2773
// Is consumed from the MRV BE. Allows disabling single validation rule.
export const MRV_DISABLE_VALIDATION_RULE_FLAGS = {
  DISABLE_VALIDATION_RULE_PLANTING_HARVEST_DATES:
    'Disable validation rule #validate_planting_harvest_dates',
  DISABLE_VALIDATION_RULE_HARVEST_DATE_IN_RECORD_YEAR:
    'Disable validation rule #validate_harvest_date_in_record_year',
  DISABLE_VALIDATION_RULE_TILLAGE_EVENT: 'Disable validation rule #validate_tillage_event',
  DISABLE_VALIDATION_RULE_CROPPING_EVENT_LENGTH:
    'Disable validation rule #validate_cropping_event_length',
  DISABLE_VALIDATION_RULE_DATE_AND_RECORD_YEAR:
    'Disable validation rule #validate_tillage_date_and_record_year',
  DISABLE_VALIDATION_RULE_CROP_TYPE_AND_CROP_USAGE:
    'Disable validation rule #validate_crop_type_and_crop_usage',
  DISABLE_VALIDATION_RULE_ADDITIVES_LIMIT: 'Disable validation rule #validate_additives_limit_attr',
  DISABLE_VALIDATION_RULE_APPLICATION_DEPTH: 'Disable validation rule #validate_application_depth',
  DISABLE_VALIDATION_RULE_APPLICATION_RATE: 'Disable validation rule #validate_application_rate',
  DISABLE_VALIDATION_RULE_WATER_AMOUNT: 'Disable validation rule #validate_water_amount',
  DISABLE_VALIDATION_RULE_PRODUCT_MEASURE_APPROVED:
    'Disable validation rule #validate_if_product_is_measure_approved',
  DISABLE_VALIDATION_RULE_ADDITIVES_MEASURE_APPROVED:
    'Disable validation rule #validate_if_additives_are_measure_approved',
  DISABLE_VALIDATION_RULE_ADDITIVES_UNIQUE_ATTR:
    'Disable validation rule #validate_additives_unique_attr',
  DISABLE_VALIDATION_RULE_PRODUCT_LIQUID_OR_DRY:
    'Disable validation rule #validate_product_liquid_or_dry',
  DISABLE_VALIDATION_RULE_CROP_TYPE: 'Disable validation rule #validate_crop_type',
  DISABLE_VALIDATION_RULE_CROP_YIELD: 'Disable validation rule #validate_crop_yield',
  DISABLE_VALIDATION_RULE_INTERVALS_OVERLAP: 'Disable validation rule #validate_intervals_overlap',
  DISABLE_VALIDATION_RULE_FLOOD_PERCENTAGE: 'Disable validation rule #validate_flood_percentage',
  DISABLE_VALIDATION_RULE_SUBSURFACE_DRIP_DEPTH:
    'Disable validation rule #validate_subsurface_drip_depth',
  DISABLE_VALIDATION_RULE_TILLAGE_DEPTH: 'Disable validation rule #validate_tillage_depth',
} as const;

// Put all the feature flags here that you wish to use in your components
export const FEATURE_FLAGS = {
  // INTRODUCED IN https://regrow.atlassian.net/browse/MRV-1983
  // REMOVED AFTER QA
  EXPLORE_API: 'Explore API',

  // INTRODUCED INhttps://regrow.atlassian.net/browse/MRV-2757
  PROGRESS_BLOCKING_VALIDATION: 'Progress-blocking validations',
  NEW_ALERTS: 'New Alerts',

  // INTRODUCED IN https://regrow.atlassian.net/browse/MRV-2987
  SIMULATIONS_OUTCOMES: 'Simulations Outcomes',

  // INTRODUCED IN https://regrow.atlassian.net/browse/MRV-3187
  // TO REMOVE IN https://regrow.atlassian.net/browse/MRV-3197
  DUPLICATE_FIELD_DATA: 'Duplicate Field Data',
  // INTRODUCED IN https://regrow.atlassian.net/browse/SR-1002
  // TO REMOVE IN After the feature is confirmed to be working well
  DUPLICATE_FIELD_DATA_PER_SINGLE_PROJECT: 'Duplicate Field Data Per Single Project',

  // https://regrow.atlassian.net/browse/MRV-3673
  DUPLICATE_FIELD_DATA_ACROSS_GROUPS: 'Duplicate Field Data Across Groups',

  // INTRODUCED IN https://regrow.atlassian.net/browse/MRV-3200
  ADD_PRODUCER_DIRECTLY: 'Add Producer Directly',

  // INTRODUCED IN https://regrow.atlassian.net/browse/MRV-2773
  ...MRV_DISABLE_VALIDATION_RULE_FLAGS,

  // https://regrow.atlassian.net/browse/MRV-3697
  ALLOW_ENROLMENT_STAGE_PROGRESS_WITHOUT_COMPLETION:
    'Allow enrolment stage progress without completion',

  // https://regrow.atlassian.net/browse/MRV-3570
  MRV_DASHBOARD_FILTER_BAR: 'Enable filter bar for applicable MRV Dashboard charts',

  // https://regrow.atlassian.net/browse/MRV-4119
  FMS_ENABLED_PROGRAM: 'Enable FMS Import for Programs',

  // https://regrow.atlassian.net/browse/SR-1604
  FARM_FIELD_DELETION_ENABLED: 'Enable Farm and Field Deletion',

  // https://regrow.atlassian.net/browse/MRV-5190
  PROGRAM_LINKAGE_AND_MIGRATION: 'Enable Program Linkage and Migration',

  // https://regrow.atlassian.net/browse/VOC-279
  PRINT_REVIEW_ESTIMATES: 'Print Review Estimates',

  // https://regrow.atlassian.net/browse/MRV-5858
  GOOGLE_BASEMAPS: 'Enable Google Basemaps',

  // https://regrow.atlassian.net/browse/MRV-3950
  MRV_COPY_FIELD_EVENTS: 'Copy field events',

  // https://regrow.atlassian.net/browse/MRV-5921
  INTENDED_PRACTICES_PREFILL: 'Intended Practices Prefill',
} as const;

/**
 * @deprecated To be removed once we have no more programs with stages that are not in config.
 * `getStageName` should be used instead, but it requires the stages to exist in the program response data,
 * which is not the case for some old programs.
 */
export const EnrollmentStepNameAliases: Record<string, () => string> = {
  [EnrollmentStep.Fields]: () =>
    t({id: 'EnrollmentStep.Fields & Boundaries', defaultMessage: 'Fields & Boundaries'}),
  [EnrollmentStep.CropPractices]: () =>
    t({id: 'EnrollmentStep.Crop history', defaultMessage: 'Crop history'}),
  [EnrollmentStep.AssignPractices]: () =>
    t({id: 'EnrollmentStep.Assign Practices', defaultMessage: 'Assign Practices'}),
  [EnrollmentStep.ViewOutcomes]: () =>
    t({id: 'EnrollmentStep.View Outcomes', defaultMessage: 'View estimated outcomes'}),
  [EnrollmentStep.Survey]: () => t({id: 'EnrollmentStep.Survey', defaultMessage: 'Survey'}),
};

export const stageTypeNames: Record<MRVStageType | string, () => string> = {
  [MRVStageType.FIELD_BOUNDARIES]: () =>
    t({id: `MRVStageType.${MRVStageType.FIELD_BOUNDARIES}`, defaultMessage: 'Fields & Boundaries'}),
  [MRVStageType.CONFIRM_HISTORY]: () =>
    t({id: `MRVStageType.${MRVStageType.CONFIRM_HISTORY}`, defaultMessage: 'Confirm History'}),
  [MRVStageType.ASSIGN_PRACTICES]: () =>
    t({id: `MRVStageType.INTENDED_PRACTICES`, defaultMessage: 'Intended Practices'}),
  [MRVStageType.VIEW_OUTCOMES]: () =>
    t({
      id: `MRVStageType.${MRVStageType.VIEW_OUTCOMES}`,
      defaultMessage: 'Review estimates',
    }),
  [MRVStageType.HISTORICAL_CROP_ROTATION]: () =>
    t({
      id: `EnrollmentStep.${MRVStageType.HISTORICAL_CROP_ROTATION}`,
      defaultMessage: 'Crop history',
    }),
  [MRVStageType.HISTORICAL_TILLAGE]: () =>
    t({id: `MRVStageType.${MRVStageType.HISTORICAL_TILLAGE}`, defaultMessage: 'Tillage History'}),
  [MRVStageType.INTENDED_COMMODITY_CROPS]: () =>
    t({
      id: `MRVStageType.${MRVStageType.INTENDED_COMMODITY_CROPS}`,
      defaultMessage: 'Intended Commodity Crops',
    }),
  [MRVStageType.SURVEY]: () =>
    t({id: `MRVStageType.${MRVStageType.SURVEY}`, defaultMessage: 'Survey'}),
  [MRVStageType.SUMMER_CROPS]: () =>
    t({id: `MRVStageType.${MRVStageType.SUMMER_CROPS}`, defaultMessage: 'Summer Crops'}),
  [MRVStageType.WINTER_CROPS]: () =>
    t({id: `MRVStageType.${MRVStageType.WINTER_CROPS}`, defaultMessage: 'Winter Crops'}),
  [MRVStageType.TILLAGE]: () =>
    t({id: `MRVStageType.${MRVStageType.TILLAGE}`, defaultMessage: 'Tillage'}),
  [MRVStageType.NUTRIENT_MGMT]: () =>
    t({
      id: `MRVStageType.${MRVStageType.NUTRIENT_MGMT}`,
      defaultMessage: 'Nutrient Management',
    }),
  [MRVStageType.NUTRIENT_MGMT_INTENDED]: () =>
    t({
      id: `MRVStageType.${MRVStageType.NUTRIENT_MGMT_INTENDED}`,
      defaultMessage: 'Intended Nutrient Management',
    }),
  [MRVStageType.CONTRACT]: () =>
    t({id: `MRVStageType.${MRVStageType.CONTRACT}`, defaultMessage: 'Contract'}),
  [MRVStageType.ELIGIBILITY]: () =>
    t({id: `MRVStageType.${MRVStageType.ELIGIBILITY}`, defaultMessage: 'Eligibility'}),
  [MRVStageType.FIELD_INFORMATION]: () =>
    t({id: `MRVStageType.${MRVStageType.FIELD_INFORMATION}`, defaultMessage: 'Field Information'}),
  [MRVStageType.IRRIGATION]: () =>
    t({id: `MRVStageType.${MRVStageType.IRRIGATION}`, defaultMessage: 'Irrigation'}),
  [MRVStageType.RICE_CROP_HISTORY]: () =>
    t({id: `MRVStageType.${MRVStageType.RICE_CROP_HISTORY}`, defaultMessage: 'Crop History'}),
  [MRVStageType.MOB_HISTORY]: () =>
    t({id: `MRVStageType.${MRVStageType.MOB_HISTORY}`, defaultMessage: 'Mob History'}),
  [MRVStageType.MOB_MOVEMENT]: () =>
    t({id: `MRVStageType.${MRVStageType.MOB_MOVEMENT}`, defaultMessage: 'Mob Movement'}),
  [MRVStageType.FARM_LEVEL_MANAGEMENT]: () =>
    t({
      id: `MRVStageType.${MRVStageType.FARM_LEVEL_MANAGEMENT}`,
      defaultMessage: 'Farm Level Management',
    }),
  [MRVStageType.NUTRIENT_EVENTS]: () =>
    t({
      id: `stage.${MRVStageType.NUTRIENT_EVENTS}.name`,
      defaultMessage: 'Nutrients',
    }),
  [MRVStageType.CROP_EVENTS]: () =>
    t({
      id: `stage.${MRVStageType.CROP_EVENTS}.name`,
      defaultMessage: 'Crops',
    }),
  [MRVStageType.IRRIGATION_EVENTS]: () =>
    t({
      id: `stage.${MRVStageType.IRRIGATION_EVENTS}.name`,
      defaultMessage: 'Irrigation',
    }),
  [MRVStageType.TILLAGE_EVENTS]: () =>
    t({
      id: `stage.${MRVStageType.TILLAGE_EVENTS}.name`,
      defaultMessage: 'Tillage',
    }),
  [MRVStageType.CHEMICAL_MANAGEMENT]: () =>
    t({
      id: `MRVStageType.${MRVStageType.CHEMICAL_MANAGEMENT}`,
      defaultMessage: 'Chemical Management',
    }),
};
