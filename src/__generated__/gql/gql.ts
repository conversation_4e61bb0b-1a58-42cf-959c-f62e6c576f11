/* eslint-disable */
import {TypedDocumentNode as DocumentNode} from '@graphql-typed-document-node/core';

import * as types from './graphql';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 */
const documents = {
  '\n  mutation createOrUpdateFieldEvent(\n    $fieldId: ID!\n    $projectId: ID!\n    $phaseId: ID!\n    $event: FieldEventInput!\n  ) {\n    createOrUpdateFieldEvent(\n      fieldId: $fieldId\n      projectId: $projectId\n      phaseId: $phaseId\n      event: $event\n    ) {\n      id\n      type\n      event_values {\n        ...FallowPeriodAttributes\n        ...CroppingEventAttributes\n        ...TillageEventAttributes\n        ...IrrigationEventAttributes\n        ...ApplicationEventAttributes\n      }\n    }\n  }\n':
    types.CreateOrUpdateFieldEventDocument,
  '\n  mutation bulkCreateOrUpdateFieldEvents($events: [BulkFieldEventInput!]!) {\n    bulkCreateOrUpdateFieldEvents(events: $events) {\n      events {\n        id\n        type\n        event_values {\n          ...FallowPeriodAttributes\n          ...CroppingEventAttributes\n          ...TillageEventAttributes\n          ...IrrigationEventAttributes\n          ...ApplicationEventAttributes\n        }\n      }\n    }\n  }\n':
    types.BulkCreateOrUpdateFieldEventsDocument,
  '\n  mutation deleteFieldEvent(\n    $projectId: ID!\n    $phaseId: ID!\n    $fieldId: ID!\n    $event: FieldEventDeleteInput!\n  ) {\n    deleteFieldEvent(projectId: $projectId, phaseId: $phaseId, fieldId: $fieldId, event: $event)\n  }\n':
    types.DeleteFieldEventDocument,
  '\n  mutation updateCultivationCycleNoPracticeObservation(\n    $projectId: ID!\n    $phaseId: ID!\n    $stageId: ID!\n    $fieldId: ID!\n    $cultivationCycleId: ID!\n    $noPracticeObservationValue: Boolean!\n  ) {\n    updateCultivationCycleNoPracticeObservation(\n      projectId: $projectId\n      phaseId: $phaseId\n      stageId: $stageId\n      fieldId: $fieldId\n      cultivationCycleId: $cultivationCycleId\n      noPracticeObservationValue: $noPracticeObservationValue\n    ) {\n      id\n      start_date\n      end_date\n      crop_type\n      no_practice_observations {\n        tillage_event\n        irrigation_event\n        application_event\n      }\n    }\n  }\n':
    types.UpdateCultivationCycleNoPracticeObservationDocument,
  '\n  mutation Mutation($events: [BulkCopyFieldEventInput!]!, $copyMode: CopyFieldEventsMode!) {\n    bulkCopyFieldEvents(events: $events, copyMode: $copyMode) {\n      id\n      type\n    }\n  }\n':
    types.MutationDocument,
  '\n  fragment CroppingEventAttributes on CroppingEvent {\n    planting_date\n    harvest_date\n    crop_type\n    crop_yield\n    yield_rate_unit\n    termination_method\n    residue_harvested\n    crop_usage\n  }\n\n  fragment FallowPeriodAttributes on FallowPeriod {\n    start_date\n    end_date\n    crop_type\n  }\n\n  fragment TillageEventAttributes on TillageEvent {\n    tillage_practice\n    tillage_date\n    tillage_depth\n    tillage_depth_unit\n    soil_inversion\n  }\n\n  fragment IrrigationEventAttributes on IrrigationEvent {\n    start_date\n    end_date\n    subsurface_drip_depth\n    subsurface_drip_depth_unit\n    flood_pct\n    irrigation_method\n  }\n\n  fragment ApplicationEventAttributes on ApplicationEvent {\n    application_product\n    application_date\n    application_rate\n    application_rate_type\n    application_rate_unit\n    application_method\n    application_depth\n    application_depth_unit\n    water_amount\n    water_amount_unit\n    additives\n  }\n\n  fragment FieldWithEvents on Field {\n    id\n    cultivation_cycles(prefillMonitoringPhase: $prefill_monitoring_phase) {\n      id\n      start_date\n      end_date\n      crop_type\n      crop_event_is_locked\n      contains_prefilled_monitoring_phase_events\n      no_practice_observations {\n        tillage_event\n        irrigation_event\n        application_event\n      }\n      events {\n        ...FieldEventAttributes\n      }\n    }\n  }\n\n  fragment FieldEventAttributes on FieldEvent {\n    id\n    type\n    is_locked\n    event_values {\n      ...FallowPeriodAttributes\n      ...CroppingEventAttributes\n      ...TillageEventAttributes\n      ...ApplicationEventAttributes\n      ...IrrigationEventAttributes\n    }\n  }\n':
    types.CroppingEventAttributesFragmentDoc,
  '\n  query getStageFieldCultivationCycleEvents(\n    $projectId: ID!\n    $fieldId: ID!\n    $stageId: ID!\n    $prefill_monitoring_phase: Boolean!\n  ) {\n    mrv {\n      project(projectId: $projectId) {\n        id\n        stage(stageId: $stageId) {\n          id\n          field(fieldId: $fieldId) {\n            ...FieldWithEvents\n          }\n        }\n      }\n    }\n  }\n':
    types.GetStageFieldCultivationCycleEventsDocument,
  '\n  query getPhaseFieldCultivationCycleEvents(\n    $projectId: ID!\n    $fieldId: ID!\n    $phaseType: String\n    $prefill_monitoring_phase: Boolean!\n  ) {\n    mrv {\n      project(projectId: $projectId) {\n        id\n        program {\n          id\n          phases(phaseType: $phaseType) {\n            id\n            stages {\n              id\n              type\n              field(fieldId: $fieldId) {\n                ...FieldWithEvents\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n':
    types.GetPhaseFieldCultivationCycleEventsDocument,
  '\n  query getPhases($projectId: ID!, $phaseType: String) {\n    mrv {\n      project(projectId: $projectId) {\n        id\n        dndc_status\n        program {\n          id\n          crediting_year\n          reporting_period_start_date\n          reporting_period_end_date\n          required_years_of_history\n          is_single_phase_data_collection\n          phases(phaseType: $phaseType) {\n            id\n            name\n            type\n            start_date\n            end_date\n            is_locked\n            crops {\n              id\n              regrow_name\n            }\n            data_query_range {\n              start_date\n              end_date\n            }\n            stages {\n              id\n              name\n              type\n              custom_name\n              description\n              locked\n              enabled\n              order\n              required\n              optis_prefill\n              icon\n            }\n          }\n        }\n      }\n    }\n  }\n':
    types.GetPhasesDocument,
  '\n  query getPhaseStageCompletion($projectId: ID!, $phaseType: String) {\n    mrv {\n      project(projectId: $projectId) {\n        id\n        program {\n          id\n          phases(phaseType: $phaseType) {\n            id\n            stages {\n              id\n              type\n              name\n              type\n              locked\n              enabled\n              order\n              required\n              percent_complete\n            }\n          }\n        }\n      }\n    }\n  }\n':
    types.GetPhaseStageCompletionDocument,
  '\n  query getProjectFarmsAndEnrolledFields($projectId: ID!, $farmId: ID) {\n    mrv {\n      project(projectId: $projectId) {\n        id\n        farms(farmId: $farmId, filterWithEnrolledFields: true) {\n          id\n          name\n          core_farm_group_id\n          fields {\n            id\n            name\n            core_farm_group_id\n            area\n            is_returning\n            baseline_year\n            md5\n            geometry {\n              type\n              coordinates\n            }\n          }\n        }\n      }\n    }\n  }\n':
    types.GetProjectFarmsAndEnrolledFieldsDocument,
  '\n  query getStageAttributes($projectId: ID!, $stageId: ID!) {\n    mrv {\n      project(projectId: $projectId) {\n        id\n        stage(stageId: $stageId) {\n          id\n          attribute_options {\n            crop_usage\n            termination_method\n            yield_rate_unit\n            application_method\n            application_product {\n              id\n              value\n              category\n              is_dry\n            }\n            application_rate_type\n            application_rate_unit\n            application_depth_unit\n            water_amount_unit\n            additives\n            irrigation_method\n            subsurface_drip_depth_unit\n            tillage_practice\n            tillage_depth_unit\n            residue_harvested\n          }\n        }\n      }\n    }\n  }\n':
    types.GetStageAttributesDocument,
  '\n  query getStageCompletion($projectId: ID!, $stageId: ID!) {\n    mrv {\n      project(projectId: $projectId) {\n        id\n        stage(stageId: $stageId) {\n          id\n          type\n          percent_complete\n          fields {\n            id\n            is_data_entry_complete\n          }\n        }\n      }\n    }\n  }\n':
    types.GetStageCompletionDocument,
};

/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = gql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function gql(source: string): unknown;

/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: '\n  mutation createOrUpdateFieldEvent(\n    $fieldId: ID!\n    $projectId: ID!\n    $phaseId: ID!\n    $event: FieldEventInput!\n  ) {\n    createOrUpdateFieldEvent(\n      fieldId: $fieldId\n      projectId: $projectId\n      phaseId: $phaseId\n      event: $event\n    ) {\n      id\n      type\n      event_values {\n        ...FallowPeriodAttributes\n        ...CroppingEventAttributes\n        ...TillageEventAttributes\n        ...IrrigationEventAttributes\n        ...ApplicationEventAttributes\n      }\n    }\n  }\n'
): (typeof documents)['\n  mutation createOrUpdateFieldEvent(\n    $fieldId: ID!\n    $projectId: ID!\n    $phaseId: ID!\n    $event: FieldEventInput!\n  ) {\n    createOrUpdateFieldEvent(\n      fieldId: $fieldId\n      projectId: $projectId\n      phaseId: $phaseId\n      event: $event\n    ) {\n      id\n      type\n      event_values {\n        ...FallowPeriodAttributes\n        ...CroppingEventAttributes\n        ...TillageEventAttributes\n        ...IrrigationEventAttributes\n        ...ApplicationEventAttributes\n      }\n    }\n  }\n'];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: '\n  mutation bulkCreateOrUpdateFieldEvents($events: [BulkFieldEventInput!]!) {\n    bulkCreateOrUpdateFieldEvents(events: $events) {\n      events {\n        id\n        type\n        event_values {\n          ...FallowPeriodAttributes\n          ...CroppingEventAttributes\n          ...TillageEventAttributes\n          ...IrrigationEventAttributes\n          ...ApplicationEventAttributes\n        }\n      }\n    }\n  }\n'
): (typeof documents)['\n  mutation bulkCreateOrUpdateFieldEvents($events: [BulkFieldEventInput!]!) {\n    bulkCreateOrUpdateFieldEvents(events: $events) {\n      events {\n        id\n        type\n        event_values {\n          ...FallowPeriodAttributes\n          ...CroppingEventAttributes\n          ...TillageEventAttributes\n          ...IrrigationEventAttributes\n          ...ApplicationEventAttributes\n        }\n      }\n    }\n  }\n'];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: '\n  mutation deleteFieldEvent(\n    $projectId: ID!\n    $phaseId: ID!\n    $fieldId: ID!\n    $event: FieldEventDeleteInput!\n  ) {\n    deleteFieldEvent(projectId: $projectId, phaseId: $phaseId, fieldId: $fieldId, event: $event)\n  }\n'
): (typeof documents)['\n  mutation deleteFieldEvent(\n    $projectId: ID!\n    $phaseId: ID!\n    $fieldId: ID!\n    $event: FieldEventDeleteInput!\n  ) {\n    deleteFieldEvent(projectId: $projectId, phaseId: $phaseId, fieldId: $fieldId, event: $event)\n  }\n'];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: '\n  mutation updateCultivationCycleNoPracticeObservation(\n    $projectId: ID!\n    $phaseId: ID!\n    $stageId: ID!\n    $fieldId: ID!\n    $cultivationCycleId: ID!\n    $noPracticeObservationValue: Boolean!\n  ) {\n    updateCultivationCycleNoPracticeObservation(\n      projectId: $projectId\n      phaseId: $phaseId\n      stageId: $stageId\n      fieldId: $fieldId\n      cultivationCycleId: $cultivationCycleId\n      noPracticeObservationValue: $noPracticeObservationValue\n    ) {\n      id\n      start_date\n      end_date\n      crop_type\n      no_practice_observations {\n        tillage_event\n        irrigation_event\n        application_event\n      }\n    }\n  }\n'
): (typeof documents)['\n  mutation updateCultivationCycleNoPracticeObservation(\n    $projectId: ID!\n    $phaseId: ID!\n    $stageId: ID!\n    $fieldId: ID!\n    $cultivationCycleId: ID!\n    $noPracticeObservationValue: Boolean!\n  ) {\n    updateCultivationCycleNoPracticeObservation(\n      projectId: $projectId\n      phaseId: $phaseId\n      stageId: $stageId\n      fieldId: $fieldId\n      cultivationCycleId: $cultivationCycleId\n      noPracticeObservationValue: $noPracticeObservationValue\n    ) {\n      id\n      start_date\n      end_date\n      crop_type\n      no_practice_observations {\n        tillage_event\n        irrigation_event\n        application_event\n      }\n    }\n  }\n'];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: '\n  mutation Mutation($events: [BulkCopyFieldEventInput!]!, $copyMode: CopyFieldEventsMode!) {\n    bulkCopyFieldEvents(events: $events, copyMode: $copyMode) {\n      id\n      type\n    }\n  }\n'
): (typeof documents)['\n  mutation Mutation($events: [BulkCopyFieldEventInput!]!, $copyMode: CopyFieldEventsMode!) {\n    bulkCopyFieldEvents(events: $events, copyMode: $copyMode) {\n      id\n      type\n    }\n  }\n'];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: '\n  fragment CroppingEventAttributes on CroppingEvent {\n    planting_date\n    harvest_date\n    crop_type\n    crop_yield\n    yield_rate_unit\n    termination_method\n    residue_harvested\n    crop_usage\n  }\n\n  fragment FallowPeriodAttributes on FallowPeriod {\n    start_date\n    end_date\n    crop_type\n  }\n\n  fragment TillageEventAttributes on TillageEvent {\n    tillage_practice\n    tillage_date\n    tillage_depth\n    tillage_depth_unit\n    soil_inversion\n  }\n\n  fragment IrrigationEventAttributes on IrrigationEvent {\n    start_date\n    end_date\n    subsurface_drip_depth\n    subsurface_drip_depth_unit\n    flood_pct\n    irrigation_method\n  }\n\n  fragment ApplicationEventAttributes on ApplicationEvent {\n    application_product\n    application_date\n    application_rate\n    application_rate_type\n    application_rate_unit\n    application_method\n    application_depth\n    application_depth_unit\n    water_amount\n    water_amount_unit\n    additives\n  }\n\n  fragment FieldWithEvents on Field {\n    id\n    cultivation_cycles(prefillMonitoringPhase: $prefill_monitoring_phase) {\n      id\n      start_date\n      end_date\n      crop_type\n      crop_event_is_locked\n      contains_prefilled_monitoring_phase_events\n      no_practice_observations {\n        tillage_event\n        irrigation_event\n        application_event\n      }\n      events {\n        ...FieldEventAttributes\n      }\n    }\n  }\n\n  fragment FieldEventAttributes on FieldEvent {\n    id\n    type\n    is_locked\n    event_values {\n      ...FallowPeriodAttributes\n      ...CroppingEventAttributes\n      ...TillageEventAttributes\n      ...ApplicationEventAttributes\n      ...IrrigationEventAttributes\n    }\n  }\n'
): (typeof documents)['\n  fragment CroppingEventAttributes on CroppingEvent {\n    planting_date\n    harvest_date\n    crop_type\n    crop_yield\n    yield_rate_unit\n    termination_method\n    residue_harvested\n    crop_usage\n  }\n\n  fragment FallowPeriodAttributes on FallowPeriod {\n    start_date\n    end_date\n    crop_type\n  }\n\n  fragment TillageEventAttributes on TillageEvent {\n    tillage_practice\n    tillage_date\n    tillage_depth\n    tillage_depth_unit\n    soil_inversion\n  }\n\n  fragment IrrigationEventAttributes on IrrigationEvent {\n    start_date\n    end_date\n    subsurface_drip_depth\n    subsurface_drip_depth_unit\n    flood_pct\n    irrigation_method\n  }\n\n  fragment ApplicationEventAttributes on ApplicationEvent {\n    application_product\n    application_date\n    application_rate\n    application_rate_type\n    application_rate_unit\n    application_method\n    application_depth\n    application_depth_unit\n    water_amount\n    water_amount_unit\n    additives\n  }\n\n  fragment FieldWithEvents on Field {\n    id\n    cultivation_cycles(prefillMonitoringPhase: $prefill_monitoring_phase) {\n      id\n      start_date\n      end_date\n      crop_type\n      crop_event_is_locked\n      contains_prefilled_monitoring_phase_events\n      no_practice_observations {\n        tillage_event\n        irrigation_event\n        application_event\n      }\n      events {\n        ...FieldEventAttributes\n      }\n    }\n  }\n\n  fragment FieldEventAttributes on FieldEvent {\n    id\n    type\n    is_locked\n    event_values {\n      ...FallowPeriodAttributes\n      ...CroppingEventAttributes\n      ...TillageEventAttributes\n      ...ApplicationEventAttributes\n      ...IrrigationEventAttributes\n    }\n  }\n'];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: '\n  query getStageFieldCultivationCycleEvents(\n    $projectId: ID!\n    $fieldId: ID!\n    $stageId: ID!\n    $prefill_monitoring_phase: Boolean!\n  ) {\n    mrv {\n      project(projectId: $projectId) {\n        id\n        stage(stageId: $stageId) {\n          id\n          field(fieldId: $fieldId) {\n            ...FieldWithEvents\n          }\n        }\n      }\n    }\n  }\n'
): (typeof documents)['\n  query getStageFieldCultivationCycleEvents(\n    $projectId: ID!\n    $fieldId: ID!\n    $stageId: ID!\n    $prefill_monitoring_phase: Boolean!\n  ) {\n    mrv {\n      project(projectId: $projectId) {\n        id\n        stage(stageId: $stageId) {\n          id\n          field(fieldId: $fieldId) {\n            ...FieldWithEvents\n          }\n        }\n      }\n    }\n  }\n'];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: '\n  query getPhaseFieldCultivationCycleEvents(\n    $projectId: ID!\n    $fieldId: ID!\n    $phaseType: String\n    $prefill_monitoring_phase: Boolean!\n  ) {\n    mrv {\n      project(projectId: $projectId) {\n        id\n        program {\n          id\n          phases(phaseType: $phaseType) {\n            id\n            stages {\n              id\n              type\n              field(fieldId: $fieldId) {\n                ...FieldWithEvents\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n'
): (typeof documents)['\n  query getPhaseFieldCultivationCycleEvents(\n    $projectId: ID!\n    $fieldId: ID!\n    $phaseType: String\n    $prefill_monitoring_phase: Boolean!\n  ) {\n    mrv {\n      project(projectId: $projectId) {\n        id\n        program {\n          id\n          phases(phaseType: $phaseType) {\n            id\n            stages {\n              id\n              type\n              field(fieldId: $fieldId) {\n                ...FieldWithEvents\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n'];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: '\n  query getPhases($projectId: ID!, $phaseType: String) {\n    mrv {\n      project(projectId: $projectId) {\n        id\n        dndc_status\n        program {\n          id\n          crediting_year\n          reporting_period_start_date\n          reporting_period_end_date\n          required_years_of_history\n          is_single_phase_data_collection\n          phases(phaseType: $phaseType) {\n            id\n            name\n            type\n            start_date\n            end_date\n            is_locked\n            crops {\n              id\n              regrow_name\n            }\n            data_query_range {\n              start_date\n              end_date\n            }\n            stages {\n              id\n              name\n              type\n              custom_name\n              description\n              locked\n              enabled\n              order\n              required\n              optis_prefill\n              icon\n            }\n          }\n        }\n      }\n    }\n  }\n'
): (typeof documents)['\n  query getPhases($projectId: ID!, $phaseType: String) {\n    mrv {\n      project(projectId: $projectId) {\n        id\n        dndc_status\n        program {\n          id\n          crediting_year\n          reporting_period_start_date\n          reporting_period_end_date\n          required_years_of_history\n          is_single_phase_data_collection\n          phases(phaseType: $phaseType) {\n            id\n            name\n            type\n            start_date\n            end_date\n            is_locked\n            crops {\n              id\n              regrow_name\n            }\n            data_query_range {\n              start_date\n              end_date\n            }\n            stages {\n              id\n              name\n              type\n              custom_name\n              description\n              locked\n              enabled\n              order\n              required\n              optis_prefill\n              icon\n            }\n          }\n        }\n      }\n    }\n  }\n'];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: '\n  query getPhaseStageCompletion($projectId: ID!, $phaseType: String) {\n    mrv {\n      project(projectId: $projectId) {\n        id\n        program {\n          id\n          phases(phaseType: $phaseType) {\n            id\n            stages {\n              id\n              type\n              name\n              type\n              locked\n              enabled\n              order\n              required\n              percent_complete\n            }\n          }\n        }\n      }\n    }\n  }\n'
): (typeof documents)['\n  query getPhaseStageCompletion($projectId: ID!, $phaseType: String) {\n    mrv {\n      project(projectId: $projectId) {\n        id\n        program {\n          id\n          phases(phaseType: $phaseType) {\n            id\n            stages {\n              id\n              type\n              name\n              type\n              locked\n              enabled\n              order\n              required\n              percent_complete\n            }\n          }\n        }\n      }\n    }\n  }\n'];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: '\n  query getProjectFarmsAndEnrolledFields($projectId: ID!, $farmId: ID) {\n    mrv {\n      project(projectId: $projectId) {\n        id\n        farms(farmId: $farmId, filterWithEnrolledFields: true) {\n          id\n          name\n          core_farm_group_id\n          fields {\n            id\n            name\n            core_farm_group_id\n            area\n            is_returning\n            baseline_year\n            md5\n            geometry {\n              type\n              coordinates\n            }\n          }\n        }\n      }\n    }\n  }\n'
): (typeof documents)['\n  query getProjectFarmsAndEnrolledFields($projectId: ID!, $farmId: ID) {\n    mrv {\n      project(projectId: $projectId) {\n        id\n        farms(farmId: $farmId, filterWithEnrolledFields: true) {\n          id\n          name\n          core_farm_group_id\n          fields {\n            id\n            name\n            core_farm_group_id\n            area\n            is_returning\n            baseline_year\n            md5\n            geometry {\n              type\n              coordinates\n            }\n          }\n        }\n      }\n    }\n  }\n'];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: '\n  query getStageAttributes($projectId: ID!, $stageId: ID!) {\n    mrv {\n      project(projectId: $projectId) {\n        id\n        stage(stageId: $stageId) {\n          id\n          attribute_options {\n            crop_usage\n            termination_method\n            yield_rate_unit\n            application_method\n            application_product {\n              id\n              value\n              category\n              is_dry\n            }\n            application_rate_type\n            application_rate_unit\n            application_depth_unit\n            water_amount_unit\n            additives\n            irrigation_method\n            subsurface_drip_depth_unit\n            tillage_practice\n            tillage_depth_unit\n            residue_harvested\n          }\n        }\n      }\n    }\n  }\n'
): (typeof documents)['\n  query getStageAttributes($projectId: ID!, $stageId: ID!) {\n    mrv {\n      project(projectId: $projectId) {\n        id\n        stage(stageId: $stageId) {\n          id\n          attribute_options {\n            crop_usage\n            termination_method\n            yield_rate_unit\n            application_method\n            application_product {\n              id\n              value\n              category\n              is_dry\n            }\n            application_rate_type\n            application_rate_unit\n            application_depth_unit\n            water_amount_unit\n            additives\n            irrigation_method\n            subsurface_drip_depth_unit\n            tillage_practice\n            tillage_depth_unit\n            residue_harvested\n          }\n        }\n      }\n    }\n  }\n'];
/**
 * The gql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function gql(
  source: '\n  query getStageCompletion($projectId: ID!, $stageId: ID!) {\n    mrv {\n      project(projectId: $projectId) {\n        id\n        stage(stageId: $stageId) {\n          id\n          type\n          percent_complete\n          fields {\n            id\n            is_data_entry_complete\n          }\n        }\n      }\n    }\n  }\n'
): (typeof documents)['\n  query getStageCompletion($projectId: ID!, $stageId: ID!) {\n    mrv {\n      project(projectId: $projectId) {\n        id\n        stage(stageId: $stageId) {\n          id\n          type\n          percent_complete\n          fields {\n            id\n            is_data_entry_complete\n          }\n        }\n      }\n    }\n  }\n'];

export function gql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> =
  TDocumentNode extends DocumentNode<infer TType, any> ? TType : never;
