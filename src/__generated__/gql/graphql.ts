/* eslint-disable */
import {TypedDocumentNode as DocumentNode} from '@graphql-typed-document-node/core';

export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends {[key: string]: unknown}> = {[K in keyof T]: T[K]};
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {[SubKey in K]?: Maybe<T[SubKey]>};
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {[SubKey in K]: Maybe<T[SubKey]>};
export type MakeEmpty<T extends {[key: string]: unknown}, K extends keyof T> = {[_ in K]?: never};
export type Incremental<T> =
  | T
  | {[P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never};
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: {input: string; output: string};
  String: {input: string; output: string};
  Boolean: {input: boolean; output: boolean};
  Int: {input: number; output: number};
  Float: {input: number; output: number};
};

export type ApplicationEvent = {
  __typename?: 'ApplicationEvent';
  additives?: Maybe<Scalars['String']['output']>;
  application_date?: Maybe<Scalars['String']['output']>;
  application_depth?: Maybe<Scalars['Float']['output']>;
  application_depth_unit?: Maybe<Scalars['String']['output']>;
  application_method?: Maybe<Scalars['String']['output']>;
  application_product?: Maybe<Scalars['String']['output']>;
  application_rate?: Maybe<Scalars['Float']['output']>;
  application_rate_type?: Maybe<Scalars['String']['output']>;
  application_rate_unit?: Maybe<Scalars['String']['output']>;
  water_amount?: Maybe<Scalars['Float']['output']>;
  water_amount_unit?: Maybe<Scalars['String']['output']>;
};

export type ApplicationProduct = {
  __typename?: 'ApplicationProduct';
  category: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  is_dry: Scalars['Boolean']['output'];
  value: Scalars['String']['output'];
};

export type BulkCopyFieldEventInput = {
  sourceEventId: Scalars['ID']['input'];
  targetFieldId: Scalars['Int']['input'];
  targetPhaseId: Scalars['Int']['input'];
  targetProjectId: Scalars['Int']['input'];
  targetStageId: Scalars['Int']['input'];
};

export type BulkFieldEventInput = {
  event_values: FieldEventValuesInput;
  fieldId: Scalars['ID']['input'];
  id?: InputMaybe<Scalars['ID']['input']>;
  phaseId: Scalars['ID']['input'];
  projectId: Scalars['ID']['input'];
  type: Scalars['String']['input'];
};

export type BulkFieldEventResponse = {
  __typename?: 'BulkFieldEventResponse';
  events: Array<BulkFieldEventResult>;
  failed: Scalars['Int']['output'];
  succeeded: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type BulkFieldEventResult = {
  __typename?: 'BulkFieldEventResult';
  error_detail?: Maybe<Scalars['String']['output']>;
  event_values?: Maybe<FieldEventValues>;
  field_id: Scalars['Int']['output'];
  id?: Maybe<Scalars['ID']['output']>;
  phase_id: Scalars['Int']['output'];
  project_id: Scalars['Int']['output'];
  status_code?: Maybe<Scalars['Int']['output']>;
  succeeded: Scalars['Boolean']['output'];
  type: Scalars['String']['output'];
};

export enum CacheControlScope {
  Private = 'PRIVATE',
  Public = 'PUBLIC',
}

export enum CopyFieldEventsMode {
  Copy = 'COPY',
  Overwrite = 'OVERWRITE',
  OverwriteLocked = 'OVERWRITE_LOCKED',
}

export type CroppingEvent = {
  __typename?: 'CroppingEvent';
  crop_type?: Maybe<Scalars['String']['output']>;
  crop_usage?: Maybe<Scalars['String']['output']>;
  crop_yield?: Maybe<Scalars['Float']['output']>;
  harvest_date?: Maybe<Scalars['String']['output']>;
  planting_date?: Maybe<Scalars['String']['output']>;
  residue_harvested?: Maybe<Scalars['String']['output']>;
  termination_method?: Maybe<Scalars['String']['output']>;
  yield_rate_unit?: Maybe<Scalars['String']['output']>;
};

export type CultivationCycle = {
  __typename?: 'CultivationCycle';
  contains_prefilled_monitoring_phase_events?: Maybe<Scalars['Boolean']['output']>;
  crop_event_is_locked?: Maybe<Scalars['Boolean']['output']>;
  crop_type?: Maybe<Scalars['String']['output']>;
  end_date: Scalars['String']['output'];
  events?: Maybe<Array<FieldEvent>>;
  id: Scalars['ID']['output'];
  no_practice_observations?: Maybe<NoPracticeObservations>;
  participates_in_phase_completion?: Maybe<Scalars['Boolean']['output']>;
  start_date: Scalars['String']['output'];
};

export type DateRange = {
  __typename?: 'DateRange';
  end_date: Scalars['String']['output'];
  start_date: Scalars['String']['output'];
};

export type DefaultApplicationProduct = {
  __typename?: 'DefaultApplicationProduct';
  category?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  is_dry?: Maybe<Scalars['Boolean']['output']>;
  regrow_name: Scalars['String']['output'];
};

export type DefaultCrop = {
  __typename?: 'DefaultCrop';
  core_id?: Maybe<Scalars['Int']['output']>;
  id: Scalars['ID']['output'];
  regrow_name: Scalars['String']['output'];
};

export type Defaults = {
  __typename?: 'Defaults';
  application_product?: Maybe<Array<DefaultApplicationProduct>>;
  crops?: Maybe<Array<DefaultCrop>>;
};

export type DefaultsApplication_ProductArgs = {
  phaseType?: InputMaybe<Scalars['String']['input']>;
};

export type DefaultsCropsArgs = {
  phaseType?: InputMaybe<Scalars['String']['input']>;
};

export type FallowPeriod = {
  __typename?: 'FallowPeriod';
  crop_type?: Maybe<Scalars['String']['output']>;
  end_date?: Maybe<Scalars['String']['output']>;
  start_date?: Maybe<Scalars['String']['output']>;
};

export type Farm = {
  __typename?: 'Farm';
  core_farm_group_id?: Maybe<Scalars['Int']['output']>;
  fields?: Maybe<Array<Field>>;
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
};

export type Field = {
  __typename?: 'Field';
  area?: Maybe<Scalars['Float']['output']>;
  baseline_year?: Maybe<Scalars['Int']['output']>;
  core_farm_group_id?: Maybe<Scalars['Int']['output']>;
  cultivation_cycles?: Maybe<Array<CultivationCycle>>;
  geometry?: Maybe<FieldGeometry>;
  id: Scalars['ID']['output'];
  is_data_entry_complete?: Maybe<Scalars['Boolean']['output']>;
  is_returning?: Maybe<Scalars['Boolean']['output']>;
  md5?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  validation_errors?: Maybe<Array<Maybe<ValidationError>>>;
};

export type FieldCultivation_CyclesArgs = {
  phaseId?: InputMaybe<Scalars['Int']['input']>;
  prefillMonitoringPhase?: InputMaybe<Scalars['Boolean']['input']>;
  stageId?: InputMaybe<Scalars['ID']['input']>;
};

export type FieldEvent = {
  __typename?: 'FieldEvent';
  event_values: FieldEventValues;
  id: Scalars['ID']['output'];
  is_locked?: Maybe<Scalars['Boolean']['output']>;
  type: Scalars['String']['output'];
};

export type FieldEventDeleteInput = {
  event_values: FieldEventValuesInput;
  id: Scalars['ID']['input'];
  is_locked?: InputMaybe<Scalars['Boolean']['input']>;
  type: Scalars['String']['input'];
};

export type FieldEventInput = {
  event_values: FieldEventValuesInput;
  id?: InputMaybe<Scalars['ID']['input']>;
  type: Scalars['String']['input'];
};

export type FieldEventValues =
  | ApplicationEvent
  | CroppingEvent
  | FallowPeriod
  | IrrigationEvent
  | TillageEvent;

export type FieldEventValuesInput = {
  additives?: InputMaybe<Scalars['String']['input']>;
  application_date?: InputMaybe<Scalars['String']['input']>;
  application_depth?: InputMaybe<Scalars['Float']['input']>;
  application_depth_unit?: InputMaybe<Scalars['String']['input']>;
  application_method?: InputMaybe<Scalars['String']['input']>;
  application_product?: InputMaybe<Scalars['String']['input']>;
  application_rate?: InputMaybe<Scalars['Float']['input']>;
  application_rate_type?: InputMaybe<Scalars['String']['input']>;
  application_rate_unit?: InputMaybe<Scalars['String']['input']>;
  crop_type?: InputMaybe<Scalars['String']['input']>;
  crop_usage?: InputMaybe<Scalars['String']['input']>;
  crop_yield?: InputMaybe<Scalars['Float']['input']>;
  end_date?: InputMaybe<Scalars['String']['input']>;
  flood_pct?: InputMaybe<Scalars['Float']['input']>;
  harvest_date?: InputMaybe<Scalars['String']['input']>;
  irrigation_method?: InputMaybe<Scalars['String']['input']>;
  planting_date?: InputMaybe<Scalars['String']['input']>;
  residue_harvested?: InputMaybe<Scalars['String']['input']>;
  soil_inversion?: InputMaybe<Scalars['Boolean']['input']>;
  start_date?: InputMaybe<Scalars['String']['input']>;
  subsurface_drip_depth?: InputMaybe<Scalars['Float']['input']>;
  subsurface_drip_depth_unit?: InputMaybe<Scalars['String']['input']>;
  termination_method?: InputMaybe<Scalars['String']['input']>;
  tillage_date?: InputMaybe<Scalars['String']['input']>;
  tillage_depth?: InputMaybe<Scalars['Float']['input']>;
  tillage_depth_unit?: InputMaybe<Scalars['String']['input']>;
  tillage_practice?: InputMaybe<Scalars['String']['input']>;
  water_amount?: InputMaybe<Scalars['Float']['input']>;
  water_amount_unit?: InputMaybe<Scalars['String']['input']>;
  yield_rate_unit?: InputMaybe<Scalars['String']['input']>;
};

export type FieldGeometry = {
  __typename?: 'FieldGeometry';
  coordinates?: Maybe<Array<Array<Array<Array<Maybe<Scalars['Float']['output']>>>>>>;
  type?: Maybe<Scalars['String']['output']>;
};

export type IrrigationEvent = {
  __typename?: 'IrrigationEvent';
  end_date?: Maybe<Scalars['String']['output']>;
  flood_pct?: Maybe<Scalars['Float']['output']>;
  irrigation_method?: Maybe<Scalars['String']['output']>;
  start_date?: Maybe<Scalars['String']['output']>;
  subsurface_drip_depth?: Maybe<Scalars['Float']['output']>;
  subsurface_drip_depth_unit?: Maybe<Scalars['String']['output']>;
};

export type Mrv = {
  __typename?: 'MRV';
  project?: Maybe<Project>;
};

export type MrvProjectArgs = {
  projectId: Scalars['ID']['input'];
};

export type Mutation = {
  __typename?: 'Mutation';
  bulkCopyFieldEvents: Array<FieldEvent>;
  bulkCreateOrUpdateFieldEvents: BulkFieldEventResponse;
  createOrUpdateFieldEvent: FieldEvent;
  deleteFieldEvent?: Maybe<Scalars['Boolean']['output']>;
  updateCultivationCycleNoPracticeObservation?: Maybe<CultivationCycle>;
};

export type MutationBulkCopyFieldEventsArgs = {
  copyMode: CopyFieldEventsMode;
  events: Array<BulkCopyFieldEventInput>;
};

export type MutationBulkCreateOrUpdateFieldEventsArgs = {
  events: Array<BulkFieldEventInput>;
};

export type MutationCreateOrUpdateFieldEventArgs = {
  event: FieldEventInput;
  fieldId: Scalars['ID']['input'];
  phaseId: Scalars['ID']['input'];
  projectId: Scalars['ID']['input'];
};

export type MutationDeleteFieldEventArgs = {
  event: FieldEventDeleteInput;
  fieldId: Scalars['ID']['input'];
  phaseId: Scalars['ID']['input'];
  projectId: Scalars['ID']['input'];
};

export type MutationUpdateCultivationCycleNoPracticeObservationArgs = {
  cultivationCycleId: Scalars['ID']['input'];
  fieldId: Scalars['ID']['input'];
  noPracticeObservationValue: Scalars['Boolean']['input'];
  phaseId: Scalars['ID']['input'];
  projectId: Scalars['ID']['input'];
  stageId: Scalars['ID']['input'];
};

export type NoPracticeObservations = {
  __typename?: 'NoPracticeObservations';
  application_event?: Maybe<Scalars['Boolean']['output']>;
  irrigation_event?: Maybe<Scalars['Boolean']['output']>;
  tillage_event?: Maybe<Scalars['Boolean']['output']>;
};

export type Phase = {
  __typename?: 'Phase';
  allow_post_close_edit?: Maybe<Scalars['Int']['output']>;
  crops?: Maybe<Array<DefaultCrop>>;
  data_query_range?: Maybe<DateRange>;
  end_date: Scalars['String']['output'];
  field?: Maybe<Field>;
  id: Scalars['ID']['output'];
  is_completed?: Maybe<Scalars['Int']['output']>;
  is_locked?: Maybe<Scalars['Boolean']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  stages?: Maybe<Array<Stage>>;
  start_date: Scalars['String']['output'];
  type: Scalars['String']['output'];
};

export type PhaseFieldArgs = {
  fieldId: Scalars['ID']['input'];
};

export type PhaseStagesArgs = {
  filterEnabled?: InputMaybe<Scalars['Boolean']['input']>;
  stageType?: InputMaybe<Scalars['String']['input']>;
};

export type Program = {
  __typename?: 'Program';
  company_name?: Maybe<Scalars['String']['output']>;
  crediting_year?: Maybe<Scalars['Int']['output']>;
  currency_code?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  is_single_phase_data_collection?: Maybe<Scalars['Boolean']['output']>;
  name: Scalars['String']['output'];
  phases?: Maybe<Array<Phase>>;
  reporting_period_end_date?: Maybe<Scalars['String']['output']>;
  reporting_period_start_date?: Maybe<Scalars['String']['output']>;
  required_years_of_history?: Maybe<Scalars['Int']['output']>;
  stage?: Maybe<Stage>;
};

export type ProgramPhasesArgs = {
  phaseType?: InputMaybe<Scalars['String']['input']>;
};

export type ProgramStageArgs = {
  stageId: Scalars['ID']['input'];
};

export type Project = {
  __typename?: 'Project';
  dndc_status?: Maybe<Scalars['String']['output']>;
  farms?: Maybe<Array<Farm>>;
  id: Scalars['ID']['output'];
  program?: Maybe<Program>;
  stage?: Maybe<Stage>;
};

export type ProjectFarmsArgs = {
  farmId?: InputMaybe<Scalars['ID']['input']>;
  filterWithEnrolledFields?: InputMaybe<Scalars['Boolean']['input']>;
};

export type ProjectStageArgs = {
  stageId: Scalars['ID']['input'];
};

export type Query = {
  __typename?: 'Query';
  currentUser?: Maybe<User>;
  defaults: Defaults;
  mrv: Mrv;
};

export type Stage = {
  __typename?: 'Stage';
  attribute_options?: Maybe<StageAttributeOptions>;
  custom_name?: Maybe<Scalars['String']['output']>;
  description?: Maybe<Scalars['String']['output']>;
  enabled: Scalars['Boolean']['output'];
  field?: Maybe<Field>;
  fields?: Maybe<Array<Field>>;
  icon?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  locked: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  optis_prefill?: Maybe<Scalars['Boolean']['output']>;
  order?: Maybe<Scalars['Int']['output']>;
  percent_complete?: Maybe<Scalars['Float']['output']>;
  phase_id?: Maybe<Scalars['Int']['output']>;
  required: Scalars['Boolean']['output'];
  type?: Maybe<Scalars['String']['output']>;
};

export type StageFieldArgs = {
  fieldId: Scalars['ID']['input'];
};

export type StageAttributeOptions = {
  __typename?: 'StageAttributeOptions';
  additives?: Maybe<Array<Scalars['String']['output']>>;
  application_depth?: Maybe<Array<Scalars['String']['output']>>;
  application_depth_unit?: Maybe<Array<Scalars['String']['output']>>;
  application_method?: Maybe<Array<Scalars['String']['output']>>;
  application_product?: Maybe<Array<ApplicationProduct>>;
  application_rate_type?: Maybe<Array<Scalars['String']['output']>>;
  application_rate_unit?: Maybe<Array<Scalars['String']['output']>>;
  crop_usage?: Maybe<Array<Scalars['String']['output']>>;
  id: Scalars['ID']['output'];
  irrigation_method?: Maybe<Array<Scalars['String']['output']>>;
  residue_harvested?: Maybe<Array<Scalars['String']['output']>>;
  subsurface_drip_depth_unit?: Maybe<Array<Scalars['String']['output']>>;
  termination_method?: Maybe<Array<Scalars['String']['output']>>;
  tillage_depth_unit?: Maybe<Array<Scalars['String']['output']>>;
  tillage_practice?: Maybe<Array<Scalars['String']['output']>>;
  water_amount_unit?: Maybe<Array<Scalars['String']['output']>>;
  yield_rate_unit?: Maybe<Array<Scalars['String']['output']>>;
};

export type TillageEvent = {
  __typename?: 'TillageEvent';
  soil_inversion?: Maybe<Scalars['Boolean']['output']>;
  tillage_date?: Maybe<Scalars['String']['output']>;
  tillage_depth?: Maybe<Scalars['Float']['output']>;
  tillage_depth_unit?: Maybe<Scalars['String']['output']>;
  tillage_practice?: Maybe<Scalars['String']['output']>;
};

export type User = {
  __typename?: 'User';
  email: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  perm: Scalars['Int']['output'];
  phone?: Maybe<Scalars['String']['output']>;
  role?: Maybe<Scalars['String']['output']>;
  surname?: Maybe<Scalars['String']['output']>;
};

export type Validation = {
  __typename?: 'Validation';
  error_messages: Array<ValidationError>;
  valid: Scalars['Boolean']['output'];
  validation_messages?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
};

export type ValidationError = {
  __typename?: 'ValidationError';
  error_message: Array<Scalars['String']['output']>;
  event_id: Scalars['ID']['output'];
};

export type CreateOrUpdateFieldEventMutationVariables = Exact<{
  fieldId: Scalars['ID']['input'];
  projectId: Scalars['ID']['input'];
  phaseId: Scalars['ID']['input'];
  event: FieldEventInput;
}>;

export type CreateOrUpdateFieldEventMutation = {
  __typename?: 'Mutation';
  createOrUpdateFieldEvent: {
    __typename?: 'FieldEvent';
    id: string;
    type: string;
    event_values:
      | ({__typename?: 'ApplicationEvent'} & {
          ' $fragmentRefs'?: {
            ApplicationEventAttributesFragment: ApplicationEventAttributesFragment;
          };
        })
      | ({__typename?: 'CroppingEvent'} & {
          ' $fragmentRefs'?: {CroppingEventAttributesFragment: CroppingEventAttributesFragment};
        })
      | ({__typename?: 'FallowPeriod'} & {
          ' $fragmentRefs'?: {FallowPeriodAttributesFragment: FallowPeriodAttributesFragment};
        })
      | ({__typename?: 'IrrigationEvent'} & {
          ' $fragmentRefs'?: {IrrigationEventAttributesFragment: IrrigationEventAttributesFragment};
        })
      | ({__typename?: 'TillageEvent'} & {
          ' $fragmentRefs'?: {TillageEventAttributesFragment: TillageEventAttributesFragment};
        });
  };
};

export type BulkCreateOrUpdateFieldEventsMutationVariables = Exact<{
  events: Array<BulkFieldEventInput> | BulkFieldEventInput;
}>;

export type BulkCreateOrUpdateFieldEventsMutation = {
  __typename?: 'Mutation';
  bulkCreateOrUpdateFieldEvents: {
    __typename?: 'BulkFieldEventResponse';
    events: Array<{
      __typename?: 'BulkFieldEventResult';
      id?: string | null;
      type: string;
      event_values?:
        | ({__typename?: 'ApplicationEvent'} & {
            ' $fragmentRefs'?: {
              ApplicationEventAttributesFragment: ApplicationEventAttributesFragment;
            };
          })
        | ({__typename?: 'CroppingEvent'} & {
            ' $fragmentRefs'?: {CroppingEventAttributesFragment: CroppingEventAttributesFragment};
          })
        | ({__typename?: 'FallowPeriod'} & {
            ' $fragmentRefs'?: {FallowPeriodAttributesFragment: FallowPeriodAttributesFragment};
          })
        | ({__typename?: 'IrrigationEvent'} & {
            ' $fragmentRefs'?: {
              IrrigationEventAttributesFragment: IrrigationEventAttributesFragment;
            };
          })
        | ({__typename?: 'TillageEvent'} & {
            ' $fragmentRefs'?: {TillageEventAttributesFragment: TillageEventAttributesFragment};
          })
        | null;
    }>;
  };
};

export type DeleteFieldEventMutationVariables = Exact<{
  projectId: Scalars['ID']['input'];
  phaseId: Scalars['ID']['input'];
  fieldId: Scalars['ID']['input'];
  event: FieldEventDeleteInput;
}>;

export type DeleteFieldEventMutation = {__typename?: 'Mutation'; deleteFieldEvent?: boolean | null};

export type UpdateCultivationCycleNoPracticeObservationMutationVariables = Exact<{
  projectId: Scalars['ID']['input'];
  phaseId: Scalars['ID']['input'];
  stageId: Scalars['ID']['input'];
  fieldId: Scalars['ID']['input'];
  cultivationCycleId: Scalars['ID']['input'];
  noPracticeObservationValue: Scalars['Boolean']['input'];
}>;

export type UpdateCultivationCycleNoPracticeObservationMutation = {
  __typename?: 'Mutation';
  updateCultivationCycleNoPracticeObservation?: {
    __typename?: 'CultivationCycle';
    id: string;
    start_date: string;
    end_date: string;
    crop_type?: string | null;
    no_practice_observations?: {
      __typename?: 'NoPracticeObservations';
      tillage_event?: boolean | null;
      irrigation_event?: boolean | null;
      application_event?: boolean | null;
    } | null;
  } | null;
};

export type MutationMutationVariables = Exact<{
  events: Array<BulkCopyFieldEventInput> | BulkCopyFieldEventInput;
  copyMode: CopyFieldEventsMode;
}>;

export type MutationMutation = {
  __typename?: 'Mutation';
  bulkCopyFieldEvents: Array<{__typename?: 'FieldEvent'; id: string; type: string}>;
};

export type CroppingEventAttributesFragment = {
  __typename?: 'CroppingEvent';
  planting_date?: string | null;
  harvest_date?: string | null;
  crop_type?: string | null;
  crop_yield?: number | null;
  yield_rate_unit?: string | null;
  termination_method?: string | null;
  residue_harvested?: string | null;
  crop_usage?: string | null;
} & {' $fragmentName'?: 'CroppingEventAttributesFragment'};

export type FallowPeriodAttributesFragment = {
  __typename?: 'FallowPeriod';
  start_date?: string | null;
  end_date?: string | null;
  crop_type?: string | null;
} & {' $fragmentName'?: 'FallowPeriodAttributesFragment'};

export type TillageEventAttributesFragment = {
  __typename?: 'TillageEvent';
  tillage_practice?: string | null;
  tillage_date?: string | null;
  tillage_depth?: number | null;
  tillage_depth_unit?: string | null;
  soil_inversion?: boolean | null;
} & {' $fragmentName'?: 'TillageEventAttributesFragment'};

export type IrrigationEventAttributesFragment = {
  __typename?: 'IrrigationEvent';
  start_date?: string | null;
  end_date?: string | null;
  subsurface_drip_depth?: number | null;
  subsurface_drip_depth_unit?: string | null;
  flood_pct?: number | null;
  irrigation_method?: string | null;
} & {' $fragmentName'?: 'IrrigationEventAttributesFragment'};

export type ApplicationEventAttributesFragment = {
  __typename?: 'ApplicationEvent';
  application_product?: string | null;
  application_date?: string | null;
  application_rate?: number | null;
  application_rate_type?: string | null;
  application_rate_unit?: string | null;
  application_method?: string | null;
  application_depth?: number | null;
  application_depth_unit?: string | null;
  water_amount?: number | null;
  water_amount_unit?: string | null;
  additives?: string | null;
} & {' $fragmentName'?: 'ApplicationEventAttributesFragment'};

export type FieldWithEventsFragment = {
  __typename?: 'Field';
  id: string;
  cultivation_cycles?: Array<{
    __typename?: 'CultivationCycle';
    id: string;
    start_date: string;
    end_date: string;
    crop_type?: string | null;
    crop_event_is_locked?: boolean | null;
    contains_prefilled_monitoring_phase_events?: boolean | null;
    no_practice_observations?: {
      __typename?: 'NoPracticeObservations';
      tillage_event?: boolean | null;
      irrigation_event?: boolean | null;
      application_event?: boolean | null;
    } | null;
    events?: Array<
      {__typename?: 'FieldEvent'} & {
        ' $fragmentRefs'?: {FieldEventAttributesFragment: FieldEventAttributesFragment};
      }
    > | null;
  }> | null;
} & {' $fragmentName'?: 'FieldWithEventsFragment'};

export type FieldEventAttributesFragment = {
  __typename?: 'FieldEvent';
  id: string;
  type: string;
  is_locked?: boolean | null;
  event_values:
    | ({__typename?: 'ApplicationEvent'} & {
        ' $fragmentRefs'?: {ApplicationEventAttributesFragment: ApplicationEventAttributesFragment};
      })
    | ({__typename?: 'CroppingEvent'} & {
        ' $fragmentRefs'?: {CroppingEventAttributesFragment: CroppingEventAttributesFragment};
      })
    | ({__typename?: 'FallowPeriod'} & {
        ' $fragmentRefs'?: {FallowPeriodAttributesFragment: FallowPeriodAttributesFragment};
      })
    | ({__typename?: 'IrrigationEvent'} & {
        ' $fragmentRefs'?: {IrrigationEventAttributesFragment: IrrigationEventAttributesFragment};
      })
    | ({__typename?: 'TillageEvent'} & {
        ' $fragmentRefs'?: {TillageEventAttributesFragment: TillageEventAttributesFragment};
      });
} & {' $fragmentName'?: 'FieldEventAttributesFragment'};

export type GetStageFieldCultivationCycleEventsQueryVariables = Exact<{
  projectId: Scalars['ID']['input'];
  fieldId: Scalars['ID']['input'];
  stageId: Scalars['ID']['input'];
  prefill_monitoring_phase: Scalars['Boolean']['input'];
}>;

export type GetStageFieldCultivationCycleEventsQuery = {
  __typename?: 'Query';
  mrv: {
    __typename?: 'MRV';
    project?: {
      __typename?: 'Project';
      id: string;
      stage?: {
        __typename?: 'Stage';
        id: string;
        field?:
          | ({__typename?: 'Field'} & {
              ' $fragmentRefs'?: {FieldWithEventsFragment: FieldWithEventsFragment};
            })
          | null;
      } | null;
    } | null;
  };
};

export type GetPhaseFieldCultivationCycleEventsQueryVariables = Exact<{
  projectId: Scalars['ID']['input'];
  fieldId: Scalars['ID']['input'];
  phaseType?: InputMaybe<Scalars['String']['input']>;
  prefill_monitoring_phase: Scalars['Boolean']['input'];
}>;

export type GetPhaseFieldCultivationCycleEventsQuery = {
  __typename?: 'Query';
  mrv: {
    __typename?: 'MRV';
    project?: {
      __typename?: 'Project';
      id: string;
      program?: {
        __typename?: 'Program';
        id: string;
        phases?: Array<{
          __typename?: 'Phase';
          id: string;
          stages?: Array<{
            __typename?: 'Stage';
            id: string;
            type?: string | null;
            field?:
              | ({__typename?: 'Field'} & {
                  ' $fragmentRefs'?: {FieldWithEventsFragment: FieldWithEventsFragment};
                })
              | null;
          }> | null;
        }> | null;
      } | null;
    } | null;
  };
};

export type GetPhasesQueryVariables = Exact<{
  projectId: Scalars['ID']['input'];
  phaseType?: InputMaybe<Scalars['String']['input']>;
}>;

export type GetPhasesQuery = {
  __typename?: 'Query';
  mrv: {
    __typename?: 'MRV';
    project?: {
      __typename?: 'Project';
      id: string;
      dndc_status?: string | null;
      program?: {
        __typename?: 'Program';
        id: string;
        crediting_year?: number | null;
        reporting_period_start_date?: string | null;
        reporting_period_end_date?: string | null;
        required_years_of_history?: number | null;
        is_single_phase_data_collection?: boolean | null;
        phases?: Array<{
          __typename?: 'Phase';
          id: string;
          name?: string | null;
          type: string;
          start_date: string;
          end_date: string;
          is_locked?: boolean | null;
          crops?: Array<{__typename?: 'DefaultCrop'; id: string; regrow_name: string}> | null;
          data_query_range?: {
            __typename?: 'DateRange';
            start_date: string;
            end_date: string;
          } | null;
          stages?: Array<{
            __typename?: 'Stage';
            id: string;
            name: string;
            type?: string | null;
            custom_name?: string | null;
            description?: string | null;
            locked: boolean;
            enabled: boolean;
            order?: number | null;
            required: boolean;
            optis_prefill?: boolean | null;
            icon?: string | null;
          }> | null;
        }> | null;
      } | null;
    } | null;
  };
};

export type GetPhaseStageCompletionQueryVariables = Exact<{
  projectId: Scalars['ID']['input'];
  phaseType?: InputMaybe<Scalars['String']['input']>;
}>;

export type GetPhaseStageCompletionQuery = {
  __typename?: 'Query';
  mrv: {
    __typename?: 'MRV';
    project?: {
      __typename?: 'Project';
      id: string;
      program?: {
        __typename?: 'Program';
        id: string;
        phases?: Array<{
          __typename?: 'Phase';
          id: string;
          stages?: Array<{
            __typename?: 'Stage';
            id: string;
            type?: string | null;
            name: string;
            locked: boolean;
            enabled: boolean;
            order?: number | null;
            required: boolean;
            percent_complete?: number | null;
          }> | null;
        }> | null;
      } | null;
    } | null;
  };
};

export type GetProjectFarmsAndEnrolledFieldsQueryVariables = Exact<{
  projectId: Scalars['ID']['input'];
  farmId?: InputMaybe<Scalars['ID']['input']>;
}>;

export type GetProjectFarmsAndEnrolledFieldsQuery = {
  __typename?: 'Query';
  mrv: {
    __typename?: 'MRV';
    project?: {
      __typename?: 'Project';
      id: string;
      farms?: Array<{
        __typename?: 'Farm';
        id: string;
        name: string;
        core_farm_group_id?: number | null;
        fields?: Array<{
          __typename?: 'Field';
          id: string;
          name: string;
          core_farm_group_id?: number | null;
          area?: number | null;
          is_returning?: boolean | null;
          baseline_year?: number | null;
          md5?: string | null;
          geometry?: {
            __typename?: 'FieldGeometry';
            type?: string | null;
            coordinates?: Array<Array<Array<Array<number | null>>>> | null;
          } | null;
        }> | null;
      }> | null;
    } | null;
  };
};

export type GetStageAttributesQueryVariables = Exact<{
  projectId: Scalars['ID']['input'];
  stageId: Scalars['ID']['input'];
}>;

export type GetStageAttributesQuery = {
  __typename?: 'Query';
  mrv: {
    __typename?: 'MRV';
    project?: {
      __typename?: 'Project';
      id: string;
      stage?: {
        __typename?: 'Stage';
        id: string;
        attribute_options?: {
          __typename?: 'StageAttributeOptions';
          crop_usage?: Array<string> | null;
          termination_method?: Array<string> | null;
          yield_rate_unit?: Array<string> | null;
          application_method?: Array<string> | null;
          application_rate_type?: Array<string> | null;
          application_rate_unit?: Array<string> | null;
          application_depth_unit?: Array<string> | null;
          water_amount_unit?: Array<string> | null;
          additives?: Array<string> | null;
          irrigation_method?: Array<string> | null;
          subsurface_drip_depth_unit?: Array<string> | null;
          tillage_practice?: Array<string> | null;
          tillage_depth_unit?: Array<string> | null;
          residue_harvested?: Array<string> | null;
          application_product?: Array<{
            __typename?: 'ApplicationProduct';
            id: string;
            value: string;
            category: string;
            is_dry: boolean;
          }> | null;
        } | null;
      } | null;
    } | null;
  };
};

export type GetStageCompletionQueryVariables = Exact<{
  projectId: Scalars['ID']['input'];
  stageId: Scalars['ID']['input'];
}>;

export type GetStageCompletionQuery = {
  __typename?: 'Query';
  mrv: {
    __typename?: 'MRV';
    project?: {
      __typename?: 'Project';
      id: string;
      stage?: {
        __typename?: 'Stage';
        id: string;
        type?: string | null;
        percent_complete?: number | null;
        fields?: Array<{
          __typename?: 'Field';
          id: string;
          is_data_entry_complete?: boolean | null;
        }> | null;
      } | null;
    } | null;
  };
};

export const FallowPeriodAttributesFragmentDoc = {
  kind: 'Document',
  definitions: [
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'FallowPeriodAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'FallowPeriod'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'start_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'end_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_type'}},
        ],
      },
    },
  ],
} as unknown as DocumentNode<FallowPeriodAttributesFragment, unknown>;
export const CroppingEventAttributesFragmentDoc = {
  kind: 'Document',
  definitions: [
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'CroppingEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'CroppingEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'planting_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'harvest_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_type'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_yield'}},
          {kind: 'Field', name: {kind: 'Name', value: 'yield_rate_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'termination_method'}},
          {kind: 'Field', name: {kind: 'Name', value: 'residue_harvested'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_usage'}},
        ],
      },
    },
  ],
} as unknown as DocumentNode<CroppingEventAttributesFragment, unknown>;
export const TillageEventAttributesFragmentDoc = {
  kind: 'Document',
  definitions: [
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'TillageEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'TillageEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_practice'}},
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_depth'}},
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_depth_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'soil_inversion'}},
        ],
      },
    },
  ],
} as unknown as DocumentNode<TillageEventAttributesFragment, unknown>;
export const ApplicationEventAttributesFragmentDoc = {
  kind: 'Document',
  definitions: [
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'ApplicationEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'ApplicationEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'application_product'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_rate'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_rate_type'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_rate_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_method'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_depth'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_depth_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'water_amount'}},
          {kind: 'Field', name: {kind: 'Name', value: 'water_amount_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'additives'}},
        ],
      },
    },
  ],
} as unknown as DocumentNode<ApplicationEventAttributesFragment, unknown>;
export const IrrigationEventAttributesFragmentDoc = {
  kind: 'Document',
  definitions: [
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'IrrigationEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'IrrigationEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'start_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'end_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'subsurface_drip_depth'}},
          {kind: 'Field', name: {kind: 'Name', value: 'subsurface_drip_depth_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'flood_pct'}},
          {kind: 'Field', name: {kind: 'Name', value: 'irrigation_method'}},
        ],
      },
    },
  ],
} as unknown as DocumentNode<IrrigationEventAttributesFragment, unknown>;
export const FieldEventAttributesFragmentDoc = {
  kind: 'Document',
  definitions: [
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'FieldEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'FieldEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'id'}},
          {kind: 'Field', name: {kind: 'Name', value: 'type'}},
          {kind: 'Field', name: {kind: 'Name', value: 'is_locked'}},
          {
            kind: 'Field',
            name: {kind: 'Name', value: 'event_values'},
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                {kind: 'FragmentSpread', name: {kind: 'Name', value: 'FallowPeriodAttributes'}},
                {kind: 'FragmentSpread', name: {kind: 'Name', value: 'CroppingEventAttributes'}},
                {kind: 'FragmentSpread', name: {kind: 'Name', value: 'TillageEventAttributes'}},
                {kind: 'FragmentSpread', name: {kind: 'Name', value: 'ApplicationEventAttributes'}},
                {kind: 'FragmentSpread', name: {kind: 'Name', value: 'IrrigationEventAttributes'}},
              ],
            },
          },
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'FallowPeriodAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'FallowPeriod'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'start_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'end_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_type'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'CroppingEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'CroppingEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'planting_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'harvest_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_type'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_yield'}},
          {kind: 'Field', name: {kind: 'Name', value: 'yield_rate_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'termination_method'}},
          {kind: 'Field', name: {kind: 'Name', value: 'residue_harvested'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_usage'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'TillageEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'TillageEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_practice'}},
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_depth'}},
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_depth_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'soil_inversion'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'ApplicationEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'ApplicationEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'application_product'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_rate'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_rate_type'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_rate_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_method'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_depth'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_depth_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'water_amount'}},
          {kind: 'Field', name: {kind: 'Name', value: 'water_amount_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'additives'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'IrrigationEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'IrrigationEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'start_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'end_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'subsurface_drip_depth'}},
          {kind: 'Field', name: {kind: 'Name', value: 'subsurface_drip_depth_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'flood_pct'}},
          {kind: 'Field', name: {kind: 'Name', value: 'irrigation_method'}},
        ],
      },
    },
  ],
} as unknown as DocumentNode<FieldEventAttributesFragment, unknown>;
export const FieldWithEventsFragmentDoc = {
  kind: 'Document',
  definitions: [
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'FieldWithEvents'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'Field'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'id'}},
          {
            kind: 'Field',
            name: {kind: 'Name', value: 'cultivation_cycles'},
            arguments: [
              {
                kind: 'Argument',
                name: {kind: 'Name', value: 'prefillMonitoringPhase'},
                value: {kind: 'Variable', name: {kind: 'Name', value: 'prefill_monitoring_phase'}},
              },
            ],
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                {kind: 'Field', name: {kind: 'Name', value: 'start_date'}},
                {kind: 'Field', name: {kind: 'Name', value: 'end_date'}},
                {kind: 'Field', name: {kind: 'Name', value: 'crop_type'}},
                {kind: 'Field', name: {kind: 'Name', value: 'crop_event_is_locked'}},
                {
                  kind: 'Field',
                  name: {kind: 'Name', value: 'contains_prefilled_monitoring_phase_events'},
                },
                {
                  kind: 'Field',
                  name: {kind: 'Name', value: 'no_practice_observations'},
                  selectionSet: {
                    kind: 'SelectionSet',
                    selections: [
                      {kind: 'Field', name: {kind: 'Name', value: 'tillage_event'}},
                      {kind: 'Field', name: {kind: 'Name', value: 'irrigation_event'}},
                      {kind: 'Field', name: {kind: 'Name', value: 'application_event'}},
                    ],
                  },
                },
                {
                  kind: 'Field',
                  name: {kind: 'Name', value: 'events'},
                  selectionSet: {
                    kind: 'SelectionSet',
                    selections: [
                      {kind: 'FragmentSpread', name: {kind: 'Name', value: 'FieldEventAttributes'}},
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'FallowPeriodAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'FallowPeriod'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'start_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'end_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_type'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'CroppingEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'CroppingEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'planting_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'harvest_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_type'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_yield'}},
          {kind: 'Field', name: {kind: 'Name', value: 'yield_rate_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'termination_method'}},
          {kind: 'Field', name: {kind: 'Name', value: 'residue_harvested'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_usage'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'TillageEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'TillageEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_practice'}},
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_depth'}},
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_depth_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'soil_inversion'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'ApplicationEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'ApplicationEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'application_product'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_rate'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_rate_type'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_rate_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_method'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_depth'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_depth_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'water_amount'}},
          {kind: 'Field', name: {kind: 'Name', value: 'water_amount_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'additives'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'IrrigationEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'IrrigationEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'start_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'end_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'subsurface_drip_depth'}},
          {kind: 'Field', name: {kind: 'Name', value: 'subsurface_drip_depth_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'flood_pct'}},
          {kind: 'Field', name: {kind: 'Name', value: 'irrigation_method'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'FieldEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'FieldEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'id'}},
          {kind: 'Field', name: {kind: 'Name', value: 'type'}},
          {kind: 'Field', name: {kind: 'Name', value: 'is_locked'}},
          {
            kind: 'Field',
            name: {kind: 'Name', value: 'event_values'},
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                {kind: 'FragmentSpread', name: {kind: 'Name', value: 'FallowPeriodAttributes'}},
                {kind: 'FragmentSpread', name: {kind: 'Name', value: 'CroppingEventAttributes'}},
                {kind: 'FragmentSpread', name: {kind: 'Name', value: 'TillageEventAttributes'}},
                {kind: 'FragmentSpread', name: {kind: 'Name', value: 'ApplicationEventAttributes'}},
                {kind: 'FragmentSpread', name: {kind: 'Name', value: 'IrrigationEventAttributes'}},
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<FieldWithEventsFragment, unknown>;
export const CreateOrUpdateFieldEventDocument = {
  kind: 'Document',
  definitions: [
    {
      kind: 'OperationDefinition',
      operation: 'mutation',
      name: {kind: 'Name', value: 'createOrUpdateFieldEvent'},
      variableDefinitions: [
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'fieldId'}},
          type: {kind: 'NonNullType', type: {kind: 'NamedType', name: {kind: 'Name', value: 'ID'}}},
        },
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'projectId'}},
          type: {kind: 'NonNullType', type: {kind: 'NamedType', name: {kind: 'Name', value: 'ID'}}},
        },
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'phaseId'}},
          type: {kind: 'NonNullType', type: {kind: 'NamedType', name: {kind: 'Name', value: 'ID'}}},
        },
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'event'}},
          type: {
            kind: 'NonNullType',
            type: {kind: 'NamedType', name: {kind: 'Name', value: 'FieldEventInput'}},
          },
        },
      ],
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {
            kind: 'Field',
            name: {kind: 'Name', value: 'createOrUpdateFieldEvent'},
            arguments: [
              {
                kind: 'Argument',
                name: {kind: 'Name', value: 'fieldId'},
                value: {kind: 'Variable', name: {kind: 'Name', value: 'fieldId'}},
              },
              {
                kind: 'Argument',
                name: {kind: 'Name', value: 'projectId'},
                value: {kind: 'Variable', name: {kind: 'Name', value: 'projectId'}},
              },
              {
                kind: 'Argument',
                name: {kind: 'Name', value: 'phaseId'},
                value: {kind: 'Variable', name: {kind: 'Name', value: 'phaseId'}},
              },
              {
                kind: 'Argument',
                name: {kind: 'Name', value: 'event'},
                value: {kind: 'Variable', name: {kind: 'Name', value: 'event'}},
              },
            ],
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                {kind: 'Field', name: {kind: 'Name', value: 'type'}},
                {
                  kind: 'Field',
                  name: {kind: 'Name', value: 'event_values'},
                  selectionSet: {
                    kind: 'SelectionSet',
                    selections: [
                      {
                        kind: 'FragmentSpread',
                        name: {kind: 'Name', value: 'FallowPeriodAttributes'},
                      },
                      {
                        kind: 'FragmentSpread',
                        name: {kind: 'Name', value: 'CroppingEventAttributes'},
                      },
                      {
                        kind: 'FragmentSpread',
                        name: {kind: 'Name', value: 'TillageEventAttributes'},
                      },
                      {
                        kind: 'FragmentSpread',
                        name: {kind: 'Name', value: 'IrrigationEventAttributes'},
                      },
                      {
                        kind: 'FragmentSpread',
                        name: {kind: 'Name', value: 'ApplicationEventAttributes'},
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'FallowPeriodAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'FallowPeriod'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'start_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'end_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_type'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'CroppingEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'CroppingEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'planting_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'harvest_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_type'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_yield'}},
          {kind: 'Field', name: {kind: 'Name', value: 'yield_rate_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'termination_method'}},
          {kind: 'Field', name: {kind: 'Name', value: 'residue_harvested'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_usage'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'TillageEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'TillageEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_practice'}},
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_depth'}},
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_depth_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'soil_inversion'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'IrrigationEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'IrrigationEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'start_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'end_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'subsurface_drip_depth'}},
          {kind: 'Field', name: {kind: 'Name', value: 'subsurface_drip_depth_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'flood_pct'}},
          {kind: 'Field', name: {kind: 'Name', value: 'irrigation_method'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'ApplicationEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'ApplicationEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'application_product'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_rate'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_rate_type'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_rate_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_method'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_depth'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_depth_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'water_amount'}},
          {kind: 'Field', name: {kind: 'Name', value: 'water_amount_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'additives'}},
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  CreateOrUpdateFieldEventMutation,
  CreateOrUpdateFieldEventMutationVariables
>;
export const BulkCreateOrUpdateFieldEventsDocument = {
  kind: 'Document',
  definitions: [
    {
      kind: 'OperationDefinition',
      operation: 'mutation',
      name: {kind: 'Name', value: 'bulkCreateOrUpdateFieldEvents'},
      variableDefinitions: [
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'events'}},
          type: {
            kind: 'NonNullType',
            type: {
              kind: 'ListType',
              type: {
                kind: 'NonNullType',
                type: {kind: 'NamedType', name: {kind: 'Name', value: 'BulkFieldEventInput'}},
              },
            },
          },
        },
      ],
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {
            kind: 'Field',
            name: {kind: 'Name', value: 'bulkCreateOrUpdateFieldEvents'},
            arguments: [
              {
                kind: 'Argument',
                name: {kind: 'Name', value: 'events'},
                value: {kind: 'Variable', name: {kind: 'Name', value: 'events'}},
              },
            ],
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                {
                  kind: 'Field',
                  name: {kind: 'Name', value: 'events'},
                  selectionSet: {
                    kind: 'SelectionSet',
                    selections: [
                      {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                      {kind: 'Field', name: {kind: 'Name', value: 'type'}},
                      {
                        kind: 'Field',
                        name: {kind: 'Name', value: 'event_values'},
                        selectionSet: {
                          kind: 'SelectionSet',
                          selections: [
                            {
                              kind: 'FragmentSpread',
                              name: {kind: 'Name', value: 'FallowPeriodAttributes'},
                            },
                            {
                              kind: 'FragmentSpread',
                              name: {kind: 'Name', value: 'CroppingEventAttributes'},
                            },
                            {
                              kind: 'FragmentSpread',
                              name: {kind: 'Name', value: 'TillageEventAttributes'},
                            },
                            {
                              kind: 'FragmentSpread',
                              name: {kind: 'Name', value: 'IrrigationEventAttributes'},
                            },
                            {
                              kind: 'FragmentSpread',
                              name: {kind: 'Name', value: 'ApplicationEventAttributes'},
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'FallowPeriodAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'FallowPeriod'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'start_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'end_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_type'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'CroppingEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'CroppingEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'planting_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'harvest_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_type'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_yield'}},
          {kind: 'Field', name: {kind: 'Name', value: 'yield_rate_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'termination_method'}},
          {kind: 'Field', name: {kind: 'Name', value: 'residue_harvested'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_usage'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'TillageEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'TillageEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_practice'}},
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_depth'}},
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_depth_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'soil_inversion'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'IrrigationEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'IrrigationEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'start_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'end_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'subsurface_drip_depth'}},
          {kind: 'Field', name: {kind: 'Name', value: 'subsurface_drip_depth_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'flood_pct'}},
          {kind: 'Field', name: {kind: 'Name', value: 'irrigation_method'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'ApplicationEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'ApplicationEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'application_product'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_rate'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_rate_type'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_rate_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_method'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_depth'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_depth_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'water_amount'}},
          {kind: 'Field', name: {kind: 'Name', value: 'water_amount_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'additives'}},
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  BulkCreateOrUpdateFieldEventsMutation,
  BulkCreateOrUpdateFieldEventsMutationVariables
>;
export const DeleteFieldEventDocument = {
  kind: 'Document',
  definitions: [
    {
      kind: 'OperationDefinition',
      operation: 'mutation',
      name: {kind: 'Name', value: 'deleteFieldEvent'},
      variableDefinitions: [
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'projectId'}},
          type: {kind: 'NonNullType', type: {kind: 'NamedType', name: {kind: 'Name', value: 'ID'}}},
        },
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'phaseId'}},
          type: {kind: 'NonNullType', type: {kind: 'NamedType', name: {kind: 'Name', value: 'ID'}}},
        },
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'fieldId'}},
          type: {kind: 'NonNullType', type: {kind: 'NamedType', name: {kind: 'Name', value: 'ID'}}},
        },
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'event'}},
          type: {
            kind: 'NonNullType',
            type: {kind: 'NamedType', name: {kind: 'Name', value: 'FieldEventDeleteInput'}},
          },
        },
      ],
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {
            kind: 'Field',
            name: {kind: 'Name', value: 'deleteFieldEvent'},
            arguments: [
              {
                kind: 'Argument',
                name: {kind: 'Name', value: 'projectId'},
                value: {kind: 'Variable', name: {kind: 'Name', value: 'projectId'}},
              },
              {
                kind: 'Argument',
                name: {kind: 'Name', value: 'phaseId'},
                value: {kind: 'Variable', name: {kind: 'Name', value: 'phaseId'}},
              },
              {
                kind: 'Argument',
                name: {kind: 'Name', value: 'fieldId'},
                value: {kind: 'Variable', name: {kind: 'Name', value: 'fieldId'}},
              },
              {
                kind: 'Argument',
                name: {kind: 'Name', value: 'event'},
                value: {kind: 'Variable', name: {kind: 'Name', value: 'event'}},
              },
            ],
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<DeleteFieldEventMutation, DeleteFieldEventMutationVariables>;
export const UpdateCultivationCycleNoPracticeObservationDocument = {
  kind: 'Document',
  definitions: [
    {
      kind: 'OperationDefinition',
      operation: 'mutation',
      name: {kind: 'Name', value: 'updateCultivationCycleNoPracticeObservation'},
      variableDefinitions: [
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'projectId'}},
          type: {kind: 'NonNullType', type: {kind: 'NamedType', name: {kind: 'Name', value: 'ID'}}},
        },
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'phaseId'}},
          type: {kind: 'NonNullType', type: {kind: 'NamedType', name: {kind: 'Name', value: 'ID'}}},
        },
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'stageId'}},
          type: {kind: 'NonNullType', type: {kind: 'NamedType', name: {kind: 'Name', value: 'ID'}}},
        },
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'fieldId'}},
          type: {kind: 'NonNullType', type: {kind: 'NamedType', name: {kind: 'Name', value: 'ID'}}},
        },
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'cultivationCycleId'}},
          type: {kind: 'NonNullType', type: {kind: 'NamedType', name: {kind: 'Name', value: 'ID'}}},
        },
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'noPracticeObservationValue'}},
          type: {
            kind: 'NonNullType',
            type: {kind: 'NamedType', name: {kind: 'Name', value: 'Boolean'}},
          },
        },
      ],
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {
            kind: 'Field',
            name: {kind: 'Name', value: 'updateCultivationCycleNoPracticeObservation'},
            arguments: [
              {
                kind: 'Argument',
                name: {kind: 'Name', value: 'projectId'},
                value: {kind: 'Variable', name: {kind: 'Name', value: 'projectId'}},
              },
              {
                kind: 'Argument',
                name: {kind: 'Name', value: 'phaseId'},
                value: {kind: 'Variable', name: {kind: 'Name', value: 'phaseId'}},
              },
              {
                kind: 'Argument',
                name: {kind: 'Name', value: 'stageId'},
                value: {kind: 'Variable', name: {kind: 'Name', value: 'stageId'}},
              },
              {
                kind: 'Argument',
                name: {kind: 'Name', value: 'fieldId'},
                value: {kind: 'Variable', name: {kind: 'Name', value: 'fieldId'}},
              },
              {
                kind: 'Argument',
                name: {kind: 'Name', value: 'cultivationCycleId'},
                value: {kind: 'Variable', name: {kind: 'Name', value: 'cultivationCycleId'}},
              },
              {
                kind: 'Argument',
                name: {kind: 'Name', value: 'noPracticeObservationValue'},
                value: {
                  kind: 'Variable',
                  name: {kind: 'Name', value: 'noPracticeObservationValue'},
                },
              },
            ],
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                {kind: 'Field', name: {kind: 'Name', value: 'start_date'}},
                {kind: 'Field', name: {kind: 'Name', value: 'end_date'}},
                {kind: 'Field', name: {kind: 'Name', value: 'crop_type'}},
                {
                  kind: 'Field',
                  name: {kind: 'Name', value: 'no_practice_observations'},
                  selectionSet: {
                    kind: 'SelectionSet',
                    selections: [
                      {kind: 'Field', name: {kind: 'Name', value: 'tillage_event'}},
                      {kind: 'Field', name: {kind: 'Name', value: 'irrigation_event'}},
                      {kind: 'Field', name: {kind: 'Name', value: 'application_event'}},
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  UpdateCultivationCycleNoPracticeObservationMutation,
  UpdateCultivationCycleNoPracticeObservationMutationVariables
>;
export const MutationDocument = {
  kind: 'Document',
  definitions: [
    {
      kind: 'OperationDefinition',
      operation: 'mutation',
      name: {kind: 'Name', value: 'Mutation'},
      variableDefinitions: [
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'events'}},
          type: {
            kind: 'NonNullType',
            type: {
              kind: 'ListType',
              type: {
                kind: 'NonNullType',
                type: {kind: 'NamedType', name: {kind: 'Name', value: 'BulkCopyFieldEventInput'}},
              },
            },
          },
        },
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'copyMode'}},
          type: {
            kind: 'NonNullType',
            type: {kind: 'NamedType', name: {kind: 'Name', value: 'CopyFieldEventsMode'}},
          },
        },
      ],
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {
            kind: 'Field',
            name: {kind: 'Name', value: 'bulkCopyFieldEvents'},
            arguments: [
              {
                kind: 'Argument',
                name: {kind: 'Name', value: 'events'},
                value: {kind: 'Variable', name: {kind: 'Name', value: 'events'}},
              },
              {
                kind: 'Argument',
                name: {kind: 'Name', value: 'copyMode'},
                value: {kind: 'Variable', name: {kind: 'Name', value: 'copyMode'}},
              },
            ],
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                {kind: 'Field', name: {kind: 'Name', value: 'type'}},
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<MutationMutation, MutationMutationVariables>;
export const GetStageFieldCultivationCycleEventsDocument = {
  kind: 'Document',
  definitions: [
    {
      kind: 'OperationDefinition',
      operation: 'query',
      name: {kind: 'Name', value: 'getStageFieldCultivationCycleEvents'},
      variableDefinitions: [
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'projectId'}},
          type: {kind: 'NonNullType', type: {kind: 'NamedType', name: {kind: 'Name', value: 'ID'}}},
        },
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'fieldId'}},
          type: {kind: 'NonNullType', type: {kind: 'NamedType', name: {kind: 'Name', value: 'ID'}}},
        },
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'stageId'}},
          type: {kind: 'NonNullType', type: {kind: 'NamedType', name: {kind: 'Name', value: 'ID'}}},
        },
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'prefill_monitoring_phase'}},
          type: {
            kind: 'NonNullType',
            type: {kind: 'NamedType', name: {kind: 'Name', value: 'Boolean'}},
          },
        },
      ],
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {
            kind: 'Field',
            name: {kind: 'Name', value: 'mrv'},
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                {
                  kind: 'Field',
                  name: {kind: 'Name', value: 'project'},
                  arguments: [
                    {
                      kind: 'Argument',
                      name: {kind: 'Name', value: 'projectId'},
                      value: {kind: 'Variable', name: {kind: 'Name', value: 'projectId'}},
                    },
                  ],
                  selectionSet: {
                    kind: 'SelectionSet',
                    selections: [
                      {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                      {
                        kind: 'Field',
                        name: {kind: 'Name', value: 'stage'},
                        arguments: [
                          {
                            kind: 'Argument',
                            name: {kind: 'Name', value: 'stageId'},
                            value: {kind: 'Variable', name: {kind: 'Name', value: 'stageId'}},
                          },
                        ],
                        selectionSet: {
                          kind: 'SelectionSet',
                          selections: [
                            {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                            {
                              kind: 'Field',
                              name: {kind: 'Name', value: 'field'},
                              arguments: [
                                {
                                  kind: 'Argument',
                                  name: {kind: 'Name', value: 'fieldId'},
                                  value: {kind: 'Variable', name: {kind: 'Name', value: 'fieldId'}},
                                },
                              ],
                              selectionSet: {
                                kind: 'SelectionSet',
                                selections: [
                                  {
                                    kind: 'FragmentSpread',
                                    name: {kind: 'Name', value: 'FieldWithEvents'},
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'FallowPeriodAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'FallowPeriod'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'start_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'end_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_type'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'CroppingEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'CroppingEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'planting_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'harvest_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_type'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_yield'}},
          {kind: 'Field', name: {kind: 'Name', value: 'yield_rate_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'termination_method'}},
          {kind: 'Field', name: {kind: 'Name', value: 'residue_harvested'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_usage'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'TillageEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'TillageEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_practice'}},
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_depth'}},
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_depth_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'soil_inversion'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'ApplicationEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'ApplicationEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'application_product'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_rate'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_rate_type'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_rate_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_method'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_depth'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_depth_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'water_amount'}},
          {kind: 'Field', name: {kind: 'Name', value: 'water_amount_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'additives'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'IrrigationEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'IrrigationEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'start_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'end_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'subsurface_drip_depth'}},
          {kind: 'Field', name: {kind: 'Name', value: 'subsurface_drip_depth_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'flood_pct'}},
          {kind: 'Field', name: {kind: 'Name', value: 'irrigation_method'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'FieldEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'FieldEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'id'}},
          {kind: 'Field', name: {kind: 'Name', value: 'type'}},
          {kind: 'Field', name: {kind: 'Name', value: 'is_locked'}},
          {
            kind: 'Field',
            name: {kind: 'Name', value: 'event_values'},
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                {kind: 'FragmentSpread', name: {kind: 'Name', value: 'FallowPeriodAttributes'}},
                {kind: 'FragmentSpread', name: {kind: 'Name', value: 'CroppingEventAttributes'}},
                {kind: 'FragmentSpread', name: {kind: 'Name', value: 'TillageEventAttributes'}},
                {kind: 'FragmentSpread', name: {kind: 'Name', value: 'ApplicationEventAttributes'}},
                {kind: 'FragmentSpread', name: {kind: 'Name', value: 'IrrigationEventAttributes'}},
              ],
            },
          },
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'FieldWithEvents'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'Field'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'id'}},
          {
            kind: 'Field',
            name: {kind: 'Name', value: 'cultivation_cycles'},
            arguments: [
              {
                kind: 'Argument',
                name: {kind: 'Name', value: 'prefillMonitoringPhase'},
                value: {kind: 'Variable', name: {kind: 'Name', value: 'prefill_monitoring_phase'}},
              },
            ],
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                {kind: 'Field', name: {kind: 'Name', value: 'start_date'}},
                {kind: 'Field', name: {kind: 'Name', value: 'end_date'}},
                {kind: 'Field', name: {kind: 'Name', value: 'crop_type'}},
                {kind: 'Field', name: {kind: 'Name', value: 'crop_event_is_locked'}},
                {
                  kind: 'Field',
                  name: {kind: 'Name', value: 'contains_prefilled_monitoring_phase_events'},
                },
                {
                  kind: 'Field',
                  name: {kind: 'Name', value: 'no_practice_observations'},
                  selectionSet: {
                    kind: 'SelectionSet',
                    selections: [
                      {kind: 'Field', name: {kind: 'Name', value: 'tillage_event'}},
                      {kind: 'Field', name: {kind: 'Name', value: 'irrigation_event'}},
                      {kind: 'Field', name: {kind: 'Name', value: 'application_event'}},
                    ],
                  },
                },
                {
                  kind: 'Field',
                  name: {kind: 'Name', value: 'events'},
                  selectionSet: {
                    kind: 'SelectionSet',
                    selections: [
                      {kind: 'FragmentSpread', name: {kind: 'Name', value: 'FieldEventAttributes'}},
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  GetStageFieldCultivationCycleEventsQuery,
  GetStageFieldCultivationCycleEventsQueryVariables
>;
export const GetPhaseFieldCultivationCycleEventsDocument = {
  kind: 'Document',
  definitions: [
    {
      kind: 'OperationDefinition',
      operation: 'query',
      name: {kind: 'Name', value: 'getPhaseFieldCultivationCycleEvents'},
      variableDefinitions: [
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'projectId'}},
          type: {kind: 'NonNullType', type: {kind: 'NamedType', name: {kind: 'Name', value: 'ID'}}},
        },
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'fieldId'}},
          type: {kind: 'NonNullType', type: {kind: 'NamedType', name: {kind: 'Name', value: 'ID'}}},
        },
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'phaseType'}},
          type: {kind: 'NamedType', name: {kind: 'Name', value: 'String'}},
        },
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'prefill_monitoring_phase'}},
          type: {
            kind: 'NonNullType',
            type: {kind: 'NamedType', name: {kind: 'Name', value: 'Boolean'}},
          },
        },
      ],
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {
            kind: 'Field',
            name: {kind: 'Name', value: 'mrv'},
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                {
                  kind: 'Field',
                  name: {kind: 'Name', value: 'project'},
                  arguments: [
                    {
                      kind: 'Argument',
                      name: {kind: 'Name', value: 'projectId'},
                      value: {kind: 'Variable', name: {kind: 'Name', value: 'projectId'}},
                    },
                  ],
                  selectionSet: {
                    kind: 'SelectionSet',
                    selections: [
                      {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                      {
                        kind: 'Field',
                        name: {kind: 'Name', value: 'program'},
                        selectionSet: {
                          kind: 'SelectionSet',
                          selections: [
                            {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                            {
                              kind: 'Field',
                              name: {kind: 'Name', value: 'phases'},
                              arguments: [
                                {
                                  kind: 'Argument',
                                  name: {kind: 'Name', value: 'phaseType'},
                                  value: {
                                    kind: 'Variable',
                                    name: {kind: 'Name', value: 'phaseType'},
                                  },
                                },
                              ],
                              selectionSet: {
                                kind: 'SelectionSet',
                                selections: [
                                  {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                                  {
                                    kind: 'Field',
                                    name: {kind: 'Name', value: 'stages'},
                                    selectionSet: {
                                      kind: 'SelectionSet',
                                      selections: [
                                        {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                                        {kind: 'Field', name: {kind: 'Name', value: 'type'}},
                                        {
                                          kind: 'Field',
                                          name: {kind: 'Name', value: 'field'},
                                          arguments: [
                                            {
                                              kind: 'Argument',
                                              name: {kind: 'Name', value: 'fieldId'},
                                              value: {
                                                kind: 'Variable',
                                                name: {kind: 'Name', value: 'fieldId'},
                                              },
                                            },
                                          ],
                                          selectionSet: {
                                            kind: 'SelectionSet',
                                            selections: [
                                              {
                                                kind: 'FragmentSpread',
                                                name: {kind: 'Name', value: 'FieldWithEvents'},
                                              },
                                            ],
                                          },
                                        },
                                      ],
                                    },
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'FallowPeriodAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'FallowPeriod'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'start_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'end_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_type'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'CroppingEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'CroppingEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'planting_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'harvest_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_type'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_yield'}},
          {kind: 'Field', name: {kind: 'Name', value: 'yield_rate_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'termination_method'}},
          {kind: 'Field', name: {kind: 'Name', value: 'residue_harvested'}},
          {kind: 'Field', name: {kind: 'Name', value: 'crop_usage'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'TillageEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'TillageEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_practice'}},
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_depth'}},
          {kind: 'Field', name: {kind: 'Name', value: 'tillage_depth_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'soil_inversion'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'ApplicationEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'ApplicationEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'application_product'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_rate'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_rate_type'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_rate_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_method'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_depth'}},
          {kind: 'Field', name: {kind: 'Name', value: 'application_depth_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'water_amount'}},
          {kind: 'Field', name: {kind: 'Name', value: 'water_amount_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'additives'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'IrrigationEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'IrrigationEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'start_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'end_date'}},
          {kind: 'Field', name: {kind: 'Name', value: 'subsurface_drip_depth'}},
          {kind: 'Field', name: {kind: 'Name', value: 'subsurface_drip_depth_unit'}},
          {kind: 'Field', name: {kind: 'Name', value: 'flood_pct'}},
          {kind: 'Field', name: {kind: 'Name', value: 'irrigation_method'}},
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'FieldEventAttributes'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'FieldEvent'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'id'}},
          {kind: 'Field', name: {kind: 'Name', value: 'type'}},
          {kind: 'Field', name: {kind: 'Name', value: 'is_locked'}},
          {
            kind: 'Field',
            name: {kind: 'Name', value: 'event_values'},
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                {kind: 'FragmentSpread', name: {kind: 'Name', value: 'FallowPeriodAttributes'}},
                {kind: 'FragmentSpread', name: {kind: 'Name', value: 'CroppingEventAttributes'}},
                {kind: 'FragmentSpread', name: {kind: 'Name', value: 'TillageEventAttributes'}},
                {kind: 'FragmentSpread', name: {kind: 'Name', value: 'ApplicationEventAttributes'}},
                {kind: 'FragmentSpread', name: {kind: 'Name', value: 'IrrigationEventAttributes'}},
              ],
            },
          },
        ],
      },
    },
    {
      kind: 'FragmentDefinition',
      name: {kind: 'Name', value: 'FieldWithEvents'},
      typeCondition: {kind: 'NamedType', name: {kind: 'Name', value: 'Field'}},
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {kind: 'Field', name: {kind: 'Name', value: 'id'}},
          {
            kind: 'Field',
            name: {kind: 'Name', value: 'cultivation_cycles'},
            arguments: [
              {
                kind: 'Argument',
                name: {kind: 'Name', value: 'prefillMonitoringPhase'},
                value: {kind: 'Variable', name: {kind: 'Name', value: 'prefill_monitoring_phase'}},
              },
            ],
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                {kind: 'Field', name: {kind: 'Name', value: 'start_date'}},
                {kind: 'Field', name: {kind: 'Name', value: 'end_date'}},
                {kind: 'Field', name: {kind: 'Name', value: 'crop_type'}},
                {kind: 'Field', name: {kind: 'Name', value: 'crop_event_is_locked'}},
                {
                  kind: 'Field',
                  name: {kind: 'Name', value: 'contains_prefilled_monitoring_phase_events'},
                },
                {
                  kind: 'Field',
                  name: {kind: 'Name', value: 'no_practice_observations'},
                  selectionSet: {
                    kind: 'SelectionSet',
                    selections: [
                      {kind: 'Field', name: {kind: 'Name', value: 'tillage_event'}},
                      {kind: 'Field', name: {kind: 'Name', value: 'irrigation_event'}},
                      {kind: 'Field', name: {kind: 'Name', value: 'application_event'}},
                    ],
                  },
                },
                {
                  kind: 'Field',
                  name: {kind: 'Name', value: 'events'},
                  selectionSet: {
                    kind: 'SelectionSet',
                    selections: [
                      {kind: 'FragmentSpread', name: {kind: 'Name', value: 'FieldEventAttributes'}},
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  GetPhaseFieldCultivationCycleEventsQuery,
  GetPhaseFieldCultivationCycleEventsQueryVariables
>;
export const GetPhasesDocument = {
  kind: 'Document',
  definitions: [
    {
      kind: 'OperationDefinition',
      operation: 'query',
      name: {kind: 'Name', value: 'getPhases'},
      variableDefinitions: [
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'projectId'}},
          type: {kind: 'NonNullType', type: {kind: 'NamedType', name: {kind: 'Name', value: 'ID'}}},
        },
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'phaseType'}},
          type: {kind: 'NamedType', name: {kind: 'Name', value: 'String'}},
        },
      ],
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {
            kind: 'Field',
            name: {kind: 'Name', value: 'mrv'},
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                {
                  kind: 'Field',
                  name: {kind: 'Name', value: 'project'},
                  arguments: [
                    {
                      kind: 'Argument',
                      name: {kind: 'Name', value: 'projectId'},
                      value: {kind: 'Variable', name: {kind: 'Name', value: 'projectId'}},
                    },
                  ],
                  selectionSet: {
                    kind: 'SelectionSet',
                    selections: [
                      {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                      {kind: 'Field', name: {kind: 'Name', value: 'dndc_status'}},
                      {
                        kind: 'Field',
                        name: {kind: 'Name', value: 'program'},
                        selectionSet: {
                          kind: 'SelectionSet',
                          selections: [
                            {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                            {kind: 'Field', name: {kind: 'Name', value: 'crediting_year'}},
                            {
                              kind: 'Field',
                              name: {kind: 'Name', value: 'reporting_period_start_date'},
                            },
                            {
                              kind: 'Field',
                              name: {kind: 'Name', value: 'reporting_period_end_date'},
                            },
                            {
                              kind: 'Field',
                              name: {kind: 'Name', value: 'required_years_of_history'},
                            },
                            {
                              kind: 'Field',
                              name: {kind: 'Name', value: 'is_single_phase_data_collection'},
                            },
                            {
                              kind: 'Field',
                              name: {kind: 'Name', value: 'phases'},
                              arguments: [
                                {
                                  kind: 'Argument',
                                  name: {kind: 'Name', value: 'phaseType'},
                                  value: {
                                    kind: 'Variable',
                                    name: {kind: 'Name', value: 'phaseType'},
                                  },
                                },
                              ],
                              selectionSet: {
                                kind: 'SelectionSet',
                                selections: [
                                  {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                                  {kind: 'Field', name: {kind: 'Name', value: 'name'}},
                                  {kind: 'Field', name: {kind: 'Name', value: 'type'}},
                                  {kind: 'Field', name: {kind: 'Name', value: 'start_date'}},
                                  {kind: 'Field', name: {kind: 'Name', value: 'end_date'}},
                                  {kind: 'Field', name: {kind: 'Name', value: 'is_locked'}},
                                  {
                                    kind: 'Field',
                                    name: {kind: 'Name', value: 'crops'},
                                    selectionSet: {
                                      kind: 'SelectionSet',
                                      selections: [
                                        {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                                        {kind: 'Field', name: {kind: 'Name', value: 'regrow_name'}},
                                      ],
                                    },
                                  },
                                  {
                                    kind: 'Field',
                                    name: {kind: 'Name', value: 'data_query_range'},
                                    selectionSet: {
                                      kind: 'SelectionSet',
                                      selections: [
                                        {kind: 'Field', name: {kind: 'Name', value: 'start_date'}},
                                        {kind: 'Field', name: {kind: 'Name', value: 'end_date'}},
                                      ],
                                    },
                                  },
                                  {
                                    kind: 'Field',
                                    name: {kind: 'Name', value: 'stages'},
                                    selectionSet: {
                                      kind: 'SelectionSet',
                                      selections: [
                                        {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                                        {kind: 'Field', name: {kind: 'Name', value: 'name'}},
                                        {kind: 'Field', name: {kind: 'Name', value: 'type'}},
                                        {kind: 'Field', name: {kind: 'Name', value: 'custom_name'}},
                                        {kind: 'Field', name: {kind: 'Name', value: 'description'}},
                                        {kind: 'Field', name: {kind: 'Name', value: 'locked'}},
                                        {kind: 'Field', name: {kind: 'Name', value: 'enabled'}},
                                        {kind: 'Field', name: {kind: 'Name', value: 'order'}},
                                        {kind: 'Field', name: {kind: 'Name', value: 'required'}},
                                        {
                                          kind: 'Field',
                                          name: {kind: 'Name', value: 'optis_prefill'},
                                        },
                                        {kind: 'Field', name: {kind: 'Name', value: 'icon'}},
                                      ],
                                    },
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<GetPhasesQuery, GetPhasesQueryVariables>;
export const GetPhaseStageCompletionDocument = {
  kind: 'Document',
  definitions: [
    {
      kind: 'OperationDefinition',
      operation: 'query',
      name: {kind: 'Name', value: 'getPhaseStageCompletion'},
      variableDefinitions: [
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'projectId'}},
          type: {kind: 'NonNullType', type: {kind: 'NamedType', name: {kind: 'Name', value: 'ID'}}},
        },
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'phaseType'}},
          type: {kind: 'NamedType', name: {kind: 'Name', value: 'String'}},
        },
      ],
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {
            kind: 'Field',
            name: {kind: 'Name', value: 'mrv'},
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                {
                  kind: 'Field',
                  name: {kind: 'Name', value: 'project'},
                  arguments: [
                    {
                      kind: 'Argument',
                      name: {kind: 'Name', value: 'projectId'},
                      value: {kind: 'Variable', name: {kind: 'Name', value: 'projectId'}},
                    },
                  ],
                  selectionSet: {
                    kind: 'SelectionSet',
                    selections: [
                      {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                      {
                        kind: 'Field',
                        name: {kind: 'Name', value: 'program'},
                        selectionSet: {
                          kind: 'SelectionSet',
                          selections: [
                            {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                            {
                              kind: 'Field',
                              name: {kind: 'Name', value: 'phases'},
                              arguments: [
                                {
                                  kind: 'Argument',
                                  name: {kind: 'Name', value: 'phaseType'},
                                  value: {
                                    kind: 'Variable',
                                    name: {kind: 'Name', value: 'phaseType'},
                                  },
                                },
                              ],
                              selectionSet: {
                                kind: 'SelectionSet',
                                selections: [
                                  {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                                  {
                                    kind: 'Field',
                                    name: {kind: 'Name', value: 'stages'},
                                    selectionSet: {
                                      kind: 'SelectionSet',
                                      selections: [
                                        {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                                        {kind: 'Field', name: {kind: 'Name', value: 'type'}},
                                        {kind: 'Field', name: {kind: 'Name', value: 'name'}},
                                        {kind: 'Field', name: {kind: 'Name', value: 'type'}},
                                        {kind: 'Field', name: {kind: 'Name', value: 'locked'}},
                                        {kind: 'Field', name: {kind: 'Name', value: 'enabled'}},
                                        {kind: 'Field', name: {kind: 'Name', value: 'order'}},
                                        {kind: 'Field', name: {kind: 'Name', value: 'required'}},
                                        {
                                          kind: 'Field',
                                          name: {kind: 'Name', value: 'percent_complete'},
                                        },
                                      ],
                                    },
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<GetPhaseStageCompletionQuery, GetPhaseStageCompletionQueryVariables>;
export const GetProjectFarmsAndEnrolledFieldsDocument = {
  kind: 'Document',
  definitions: [
    {
      kind: 'OperationDefinition',
      operation: 'query',
      name: {kind: 'Name', value: 'getProjectFarmsAndEnrolledFields'},
      variableDefinitions: [
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'projectId'}},
          type: {kind: 'NonNullType', type: {kind: 'NamedType', name: {kind: 'Name', value: 'ID'}}},
        },
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'farmId'}},
          type: {kind: 'NamedType', name: {kind: 'Name', value: 'ID'}},
        },
      ],
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {
            kind: 'Field',
            name: {kind: 'Name', value: 'mrv'},
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                {
                  kind: 'Field',
                  name: {kind: 'Name', value: 'project'},
                  arguments: [
                    {
                      kind: 'Argument',
                      name: {kind: 'Name', value: 'projectId'},
                      value: {kind: 'Variable', name: {kind: 'Name', value: 'projectId'}},
                    },
                  ],
                  selectionSet: {
                    kind: 'SelectionSet',
                    selections: [
                      {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                      {
                        kind: 'Field',
                        name: {kind: 'Name', value: 'farms'},
                        arguments: [
                          {
                            kind: 'Argument',
                            name: {kind: 'Name', value: 'farmId'},
                            value: {kind: 'Variable', name: {kind: 'Name', value: 'farmId'}},
                          },
                          {
                            kind: 'Argument',
                            name: {kind: 'Name', value: 'filterWithEnrolledFields'},
                            value: {kind: 'BooleanValue', value: true},
                          },
                        ],
                        selectionSet: {
                          kind: 'SelectionSet',
                          selections: [
                            {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                            {kind: 'Field', name: {kind: 'Name', value: 'name'}},
                            {kind: 'Field', name: {kind: 'Name', value: 'core_farm_group_id'}},
                            {
                              kind: 'Field',
                              name: {kind: 'Name', value: 'fields'},
                              selectionSet: {
                                kind: 'SelectionSet',
                                selections: [
                                  {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                                  {kind: 'Field', name: {kind: 'Name', value: 'name'}},
                                  {
                                    kind: 'Field',
                                    name: {kind: 'Name', value: 'core_farm_group_id'},
                                  },
                                  {kind: 'Field', name: {kind: 'Name', value: 'area'}},
                                  {kind: 'Field', name: {kind: 'Name', value: 'is_returning'}},
                                  {kind: 'Field', name: {kind: 'Name', value: 'baseline_year'}},
                                  {kind: 'Field', name: {kind: 'Name', value: 'md5'}},
                                  {
                                    kind: 'Field',
                                    name: {kind: 'Name', value: 'geometry'},
                                    selectionSet: {
                                      kind: 'SelectionSet',
                                      selections: [
                                        {kind: 'Field', name: {kind: 'Name', value: 'type'}},
                                        {kind: 'Field', name: {kind: 'Name', value: 'coordinates'}},
                                      ],
                                    },
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<
  GetProjectFarmsAndEnrolledFieldsQuery,
  GetProjectFarmsAndEnrolledFieldsQueryVariables
>;
export const GetStageAttributesDocument = {
  kind: 'Document',
  definitions: [
    {
      kind: 'OperationDefinition',
      operation: 'query',
      name: {kind: 'Name', value: 'getStageAttributes'},
      variableDefinitions: [
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'projectId'}},
          type: {kind: 'NonNullType', type: {kind: 'NamedType', name: {kind: 'Name', value: 'ID'}}},
        },
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'stageId'}},
          type: {kind: 'NonNullType', type: {kind: 'NamedType', name: {kind: 'Name', value: 'ID'}}},
        },
      ],
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {
            kind: 'Field',
            name: {kind: 'Name', value: 'mrv'},
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                {
                  kind: 'Field',
                  name: {kind: 'Name', value: 'project'},
                  arguments: [
                    {
                      kind: 'Argument',
                      name: {kind: 'Name', value: 'projectId'},
                      value: {kind: 'Variable', name: {kind: 'Name', value: 'projectId'}},
                    },
                  ],
                  selectionSet: {
                    kind: 'SelectionSet',
                    selections: [
                      {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                      {
                        kind: 'Field',
                        name: {kind: 'Name', value: 'stage'},
                        arguments: [
                          {
                            kind: 'Argument',
                            name: {kind: 'Name', value: 'stageId'},
                            value: {kind: 'Variable', name: {kind: 'Name', value: 'stageId'}},
                          },
                        ],
                        selectionSet: {
                          kind: 'SelectionSet',
                          selections: [
                            {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                            {
                              kind: 'Field',
                              name: {kind: 'Name', value: 'attribute_options'},
                              selectionSet: {
                                kind: 'SelectionSet',
                                selections: [
                                  {kind: 'Field', name: {kind: 'Name', value: 'crop_usage'}},
                                  {
                                    kind: 'Field',
                                    name: {kind: 'Name', value: 'termination_method'},
                                  },
                                  {kind: 'Field', name: {kind: 'Name', value: 'yield_rate_unit'}},
                                  {
                                    kind: 'Field',
                                    name: {kind: 'Name', value: 'application_method'},
                                  },
                                  {
                                    kind: 'Field',
                                    name: {kind: 'Name', value: 'application_product'},
                                    selectionSet: {
                                      kind: 'SelectionSet',
                                      selections: [
                                        {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                                        {kind: 'Field', name: {kind: 'Name', value: 'value'}},
                                        {kind: 'Field', name: {kind: 'Name', value: 'category'}},
                                        {kind: 'Field', name: {kind: 'Name', value: 'is_dry'}},
                                      ],
                                    },
                                  },
                                  {
                                    kind: 'Field',
                                    name: {kind: 'Name', value: 'application_rate_type'},
                                  },
                                  {
                                    kind: 'Field',
                                    name: {kind: 'Name', value: 'application_rate_unit'},
                                  },
                                  {
                                    kind: 'Field',
                                    name: {kind: 'Name', value: 'application_depth_unit'},
                                  },
                                  {kind: 'Field', name: {kind: 'Name', value: 'water_amount_unit'}},
                                  {kind: 'Field', name: {kind: 'Name', value: 'additives'}},
                                  {kind: 'Field', name: {kind: 'Name', value: 'irrigation_method'}},
                                  {
                                    kind: 'Field',
                                    name: {kind: 'Name', value: 'subsurface_drip_depth_unit'},
                                  },
                                  {kind: 'Field', name: {kind: 'Name', value: 'tillage_practice'}},
                                  {
                                    kind: 'Field',
                                    name: {kind: 'Name', value: 'tillage_depth_unit'},
                                  },
                                  {kind: 'Field', name: {kind: 'Name', value: 'residue_harvested'}},
                                ],
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<GetStageAttributesQuery, GetStageAttributesQueryVariables>;
export const GetStageCompletionDocument = {
  kind: 'Document',
  definitions: [
    {
      kind: 'OperationDefinition',
      operation: 'query',
      name: {kind: 'Name', value: 'getStageCompletion'},
      variableDefinitions: [
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'projectId'}},
          type: {kind: 'NonNullType', type: {kind: 'NamedType', name: {kind: 'Name', value: 'ID'}}},
        },
        {
          kind: 'VariableDefinition',
          variable: {kind: 'Variable', name: {kind: 'Name', value: 'stageId'}},
          type: {kind: 'NonNullType', type: {kind: 'NamedType', name: {kind: 'Name', value: 'ID'}}},
        },
      ],
      selectionSet: {
        kind: 'SelectionSet',
        selections: [
          {
            kind: 'Field',
            name: {kind: 'Name', value: 'mrv'},
            selectionSet: {
              kind: 'SelectionSet',
              selections: [
                {
                  kind: 'Field',
                  name: {kind: 'Name', value: 'project'},
                  arguments: [
                    {
                      kind: 'Argument',
                      name: {kind: 'Name', value: 'projectId'},
                      value: {kind: 'Variable', name: {kind: 'Name', value: 'projectId'}},
                    },
                  ],
                  selectionSet: {
                    kind: 'SelectionSet',
                    selections: [
                      {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                      {
                        kind: 'Field',
                        name: {kind: 'Name', value: 'stage'},
                        arguments: [
                          {
                            kind: 'Argument',
                            name: {kind: 'Name', value: 'stageId'},
                            value: {kind: 'Variable', name: {kind: 'Name', value: 'stageId'}},
                          },
                        ],
                        selectionSet: {
                          kind: 'SelectionSet',
                          selections: [
                            {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                            {kind: 'Field', name: {kind: 'Name', value: 'type'}},
                            {kind: 'Field', name: {kind: 'Name', value: 'percent_complete'}},
                            {
                              kind: 'Field',
                              name: {kind: 'Name', value: 'fields'},
                              selectionSet: {
                                kind: 'SelectionSet',
                                selections: [
                                  {kind: 'Field', name: {kind: 'Name', value: 'id'}},
                                  {
                                    kind: 'Field',
                                    name: {kind: 'Name', value: 'is_data_entry_complete'},
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
  ],
} as unknown as DocumentNode<GetStageCompletionQuery, GetStageCompletionQueryVariables>;
