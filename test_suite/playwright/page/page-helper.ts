import type {Page} from '@playwright/test';
import {ENVIRONMENT_VARIABLE_KEYS} from '../constants/environment-variable-keys';
import {getPlaywrightEnvironmentVariable, getEnvironmentVariables} from '../utils/env';
import {waitForAPI} from '../api/api-utils';
import {API_ROUTES} from '../constants/api-routes';
import type {AnyNavigationRoute} from '../constants/navigation-routes';

export const initPageWithUserToken = async (page: Page, token: string) => {
  const [baseURL] = getEnvironmentVariables([ENVIRONMENT_VARIABLE_KEYS.BASE_URL]);
  // <PERSON><PERSON> doesn't have an API to set the local storage, so we need to inject an initializing script
  // This needs to happen in the page initialization
  await page.addInitScript(value => {
    window.localStorage.setItem('token', value);
    // so we can view the generated programs in the programs list table
    window.localStorage.setItem('mrv-programsListFilteredProgramTypes', '["automation"]');
    window.localStorage.setItem('mrv-programsListViewMode', 'table');
  }, token);

  const waitForProfile = waitForAPI(page, API_ROUTES.PROFILE, {status: 200, exactURL: true});
  await page.goto(baseURL);
  await waitForProfile;
};

/**
 * @deprecated Refactor usages of waitForURL in favour of adding more assertions to the tests.
 */
export const waitForURL = async (
  page: Page,
  url: AnyNavigationRoute,
  options?: {
    timeout?: number;
    exactURL?: boolean;
  }
): Promise<void> => {
  const HOST_URL = getPlaywrightEnvironmentVariable(ENVIRONMENT_VARIABLE_KEYS.HOST_URL);
  const expectedURL = options?.exactURL && typeof url === 'string' ? `${HOST_URL}${url}` : url;

  return page
    .waitForURL((urlRes: URL) => {
      const localURL = urlRes.toString();
      if (typeof expectedURL === 'string') {
        if (options?.exactURL) {
          return localURL === expectedURL;
        } else {
          return localURL.includes(expectedURL);
        }
      } else {
        return !!localURL.match(expectedURL);
      }
    })
    .catch(() => {
      throw new Error(
        `waitForURL failed. Couldn't find ${options?.exactURL ? `exact ` : ''} URL: ${expectedURL}, 
          at page: ${page.url()}. 
          Timestamp: ${Date.now()}
        `
      );
    });
};
