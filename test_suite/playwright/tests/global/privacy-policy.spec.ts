import {test, expect} from '@playwright/test';
import {LoginPageOM} from '../../page-object-models/login.page';
import {NAVIGATION_ROUTES} from '../../constants/navigation-routes';
import {navigateToLoginPage} from '../../utils/shared/navigateToLogin';

test.describe('Privacy Policy', () => {
  test.slow();

  test('Can click through and render the privacy policy', async ({page}) => {
    const LoginPage = new LoginPageOM(page);

    await navigateToLoginPage(page);
    await expect(LoginPage.privacyPolicyLink).toBeVisible();
    await LoginPage.privacyPolicyLink.click();
    const newTabPromise = page.waitForEvent('popup');
    const newTab = await newTabPromise;
    await newTab.waitForLoadState('load');

    await expect(newTab.getByRole('heading', {name: 'Privacy Policy', exact: true})).toBeVisible();
    expect(newTab.url()).toContain('regrow.ag/privacy-policy');
  });

  test('Can navigate directly and render the privacy policy', async ({page}) => {
    await page.goto(NAVIGATION_ROUTES.PRIVACY_POLICY_FULL);
    await expect(page.getByRole('heading', {name: 'Privacy Policy', exact: true})).toBeVisible();
  });
});
